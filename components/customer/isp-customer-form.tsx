"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

const provinceDistrictMap: Record<string, string[]> = {
  Bagmati: [
    "Bhaktapur",
    "Chitwan",
    "<PERSON><PERSON><PERSON>",
    "Dolakha",
    "Kathmandu",
    "Kavrepalanchok (Kavre)",
    "Lalitpur",
    "Makwanpur",
    "Nuwakot",
    "Ramechhap",
    "Rasuwa",
    "Sindhuli",
    "Sindhupalchok",
  ],
  Gandaki: [
    "Baglung",
    "Gorkha",
    "Kaski",
    "Lamjung",
    "Manang",
    "Mustang",
    "Myagdi",
    "Nawalpur",
    "Parbat",
    "Syangja",
    "Tanahun",
  ],
  Lumbini: [
    "Arghakhanchi",
    "Banke",
    "Bardiya",
    "Dang",
    "Eastern Rukum",
    "Gulmi",
    "<PERSON><PERSON><PERSON>vas<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  ],
};

const districtMunicipalityMap: Record<string, string[]> = {
  Kathmandu: [
    "Kathmandu Metropolitan City",
    "Kageshwori-Manohara",
    "Gokarneshwor",
    "Budhanilkantha",
    "Tokha",
    "Tarakeshwar",
    "Nagarjun",
    "Chandragiri",
    "Dakshinkali",
    "Shankharapur",
  ],
  Lalitpur: [
    "Lalitpur Metropolitan City",
    "Mahalaxmi",
    "Godawari",
    "Konjyosom",
    "Bagmati",
    "Mahankal",
  ],
  Bhaktapur: ["Bhaktapur", "Madhyapur Thimi", "Changunarayan", "Suryabinayak"],
};

const getTodayDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0]; // Format: YYYY-MM-DD
};

export default function CustomerAddForm() {
  const [form, setForm] = useState({
    organization: "",
    service: "",
    package: "",
    packagePeriod: "",
    branch: "",
    firstName: "",
    lastName: "",
    username: "",
    customerType: "",
    mobile: "",
    email: "",
    province: "",
    district: "",
    municipality: "",
    ward: "",
    tole: "",
    address: "",
    idType: "",
    idNumber: "",
    registerDate: getTodayDate(),
  });

  const handleChange = (field: keyof typeof form, value: string) => {
    if (field === "province") {
      setForm({ ...form, province: value, district: "", municipality: "" });
    } else if (field === "district") {
      setForm({ ...form, district: value, municipality: "" });
    } else {
      setForm({ ...form, [field]: value });
    }
  };

  const handleSubmit = () => {
    console.log("Submitted:", form);
  };

  return (
    <div className="bg-white p-5 rounded shadow mx-auto space-y-5">
      <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Organization Name</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.organization}
            onChange={(e) => handleChange("organization", e.target.value)}
          >
            <option value="">Select</option>
            <option value="Workalaya">Workalaya</option>
            <option value="Sky Broadband">Sky Broadband</option>
            <option value="CNC">CNC</option>
          </select>
        </div>

        <div>
          <Label>Service</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.service}
            onChange={(e) => handleChange("service", e.target.value)}
          >
            <option value="">Select</option>
            <option value="Dedicated-Line">Dedicated Line</option>
            <option value="FTTH">FTTH</option>
            <option value="SOHO">SOHO</option>
            <option value="IPtv">IPTV</option>
          </select>
        </div>

        <div>
          <Label>Package</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.package}
            onChange={(e) => handleChange("package", e.target.value)}
          >
            <option value="">Select</option>
            <option value="50Mbps">50Mbps</option>
            <option value="100Mbps">100Mbps</option>
            <option value="150Mbps">150Mbps</option>
            <option value="200Mbps">200Mbps</option>
          </select>
        </div>

        <div>
          <Label>Package Period</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.packagePeriod}
            onChange={(e) => handleChange("packagePeriod", e.target.value)}
          >
            <option value="">Select</option>
            <option value="1 Month">1 Month</option>
            <option value="3 Month">3 Month</option>
            <option value="6 Month">6 Month</option>
            <option value="12 Month">12 Month</option>
          </select>
        </div>

        <div>
          <Label>Branch</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.branch}
            onChange={(e) => handleChange("branch", e.target.value)}
          >
            <option value="">Select</option>
            <option value="Kapan">Kapan</option>
            <option value="Koteshwor">Koteshwor</option>
            <option value="Tinkune">Tinkune</option>
            <option value="Lalitpur">Lalitpur</option>
            <option value="Chabahil">Chabahil</option>
          </select>
        </div>

        <div>
          <Label>First Name</Label>
          <Input
            type="text"
            value={form.firstName}
            onChange={(e) => handleChange("firstName", e.target.value)}
          />
        </div>

        <div>
          <Label>Last Name</Label>
          <Input
            type="text"
            value={form.lastName}
            onChange={(e) => handleChange("lastName", e.target.value)}
          />
        </div>

        <div>
          <Label>Username</Label>
          <Input
            type="text"
            value={form.username}
            onChange={(e) => handleChange("username", e.target.value)}
          />
        </div>

        <div>
          <Label>Customer Type</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.customerType}
            onChange={(e) => handleChange("customerType", e.target.value)}
          >
            <option value="">Select</option>
            <option value="Home">Home</option>
            <option value="Business">Business</option>
          </select>
        </div>

        <div>
          <Label>Mobile Number</Label>
          <Input
            type="tel"
            value={form.mobile}
            onChange={(e) => handleChange("mobile", e.target.value)}
          />
        </div>

        <div>
          <Label>Email</Label>
          <Input
            type="email"
            value={form.email}
            onChange={(e) => handleChange("email", e.target.value)}
          />
        </div>

        <div>
          <Label>Province</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.province}
            onChange={(e) => handleChange("province", e.target.value)}
          >
            <option value="">Select Province</option>
            {Object.keys(provinceDistrictMap).map((province) => (
              <option key={province} value={province}>
                {province}
              </option>
            ))}
          </select>
        </div>

        <div>
          <Label>District</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.district}
            onChange={(e) => handleChange("district", e.target.value)}
            disabled={!form.province}
          >
            <option value="">Select District</option>
            {form.province &&
              provinceDistrictMap[form.province].map((district) => (
                <option key={district} value={district}>
                  {district}
                </option>
              ))}
          </select>
        </div>

        <div>
          <Label>Municipality / Rural Municipality</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.municipality}
            onChange={(e) => handleChange("municipality", e.target.value)}
            disabled={!form.district || !districtMunicipalityMap[form.district]}
          >
            <option value="">Select Municipality</option>
            {form.district &&
              districtMunicipalityMap[form.district]?.map((mun) => (
                <option key={mun} value={mun}>
                  {mun}
                </option>
              ))}
          </select>
        </div>

        <div>
          <Label>Ward Number (Optional)</Label>
          <Input
            type="text"
            value={form.ward}
            onChange={(e) => handleChange("ward", e.target.value)}
          />
        </div>

        <div>
          <Label>Tole/Street Name (Optional)</Label>
          <Input
            type="text"
            value={form.tole}
            onChange={(e) => handleChange("tole", e.target.value)}
          />
        </div>

        <div>
          <Label>Address</Label>
          <Input
            type="text"
            value={form.address}
            onChange={(e) => handleChange("address", e.target.value)}
          />
        </div>

        <div>
          <Label>ID Type</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.idType}
            onChange={(e) => handleChange("idType", e.target.value)}
          >
            <option value="">Select</option>
            <option value="Citizenship">Citizenship</option>
            <option value="NID">NID</option>
            <option value="Voter Card">Voter Card</option>
            <option value="Passport">Passport</option>
          </select>
        </div>

        <div>
          <Label>ID Number</Label>
          <Input
            type="text"
            value={form.idNumber}
            onChange={(e) => handleChange("idNumber", e.target.value)}
          />
        </div>

        <div>
          <Label>Register Date</Label>
          <Input
            type="date"
            value={form.registerDate}
            onChange={(e) => handleChange("registerDate", e.target.value)}
          />
        </div>
      </div>

      <div>
        <Button className="w-full sm:w-auto" size="sm" onClick={handleSubmit}>
          Submit
        </Button>
      </div>
    </div>
  );
}
