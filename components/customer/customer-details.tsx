"use client";

import { useState, useRef, useEffect } from "react";
import { Plus, SquarePen, ChevronLeft, ChevronRight, UserRoundPlus } from "lucide-react";
// import { useState } from "react";
import ISPCustomerForm from "@/components/customer/isp-customer-form";
import CustomerView from "@/components/view-customer";
import { Button } from "@/components/ui/button";
import { customers } from "@/app/data/customers"; // Import customers
// import { Plus, SquarePen, ChevronLeft, ChevronRight } from "lucide-react";

// Define possible filter states
type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function CustomersDetails() {
  const [showForm, setShowForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");

  // Create a ref for the top-level div of THIS component
  const componentRef = useRef<HTMLDivElement>(null);

  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? customers.length
      : parseInt(selectedItemsPerPageValue, 10);

  const filteredCustomers = customers.filter((customer) => {
    if (currentFilter === "All") {
      return true;
    }
    return customer.status === currentFilter;
  });

  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    const usernameA = a.username.toLowerCase();
    const usernameB = b.username.toLowerCase(); // Corrected line
    if (usernameA < usernameB) {
      return -1;
    }
    if (usernameA > usernameB) {
      return 1;
    }
    return 0;
  });

  const totalPages =
    itemsPerPage === sortedCustomers.length
      ? 1
      : Math.ceil(sortedCustomers.length / itemsPerPage);

  const currentCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  /**
   * Helper function to scroll the closest scrollable parent to the top.
   * This is more reliable when the component itself isn't the direct scroll container.
   */
  const scrollToTop = () => {
    if (componentRef.current) {
      // Find the closest parent with overflow-y: auto or scroll
      let currentElement: HTMLElement | null = componentRef.current;
      while (currentElement && currentElement !== document.body) {
        const style = window.getComputedStyle(currentElement);
        if (style.overflowY === "auto" || style.overflowY === "scroll") {
          currentElement.scrollTop = 0;
          return; // Found and scrolled the container
        }
        currentElement = currentElement.parentElement;
      }
      // Fallback to window scroll if no specific scrollable parent is found within the component's hierarchy
      window.scrollTo(0, 0);
    }
  };

  const handleShowFormToggle = () => {
    setShowForm(!showForm);
    setSelectedCustomer(null);
    scrollToTop(); // Scroll to top when toggling mode
  };

  const handleSelectCustomer = (customer: any) => {
    setSelectedCustomer(customer);
    setShowForm(false); // Hide form when a customer is selected for view
    scrollToTop(); // Scroll to top when a customer is selected
  };

  const handleBackToList = () => {
    setSelectedCustomer(null);
    scrollToTop(); // Scroll to top when going back to list view
  };

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    setCurrentPage(1);
    setSelectedCustomer(null);
    setShowForm(false);
    scrollToTop(); // Scroll to top when applying a new filter
  };

  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
    scrollToTop(); // Scroll to top when changing items per page
  };

  return (
    // Attach the ref to the outermost div of your component
    <div
      ref={componentRef}
      className="mx-auto bg-white rounded-lg p-5 sm:p-6 lg:p-5 max-w-full"
    >
      {!selectedCustomer && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-3 mb-3">
          <h1 className="text-x sm:text-x font-bold text-center sm:text-left w-full sm:w-auto">
            {showForm ? "Add Customer" : "ISP Customers"}
          </h1>
          <Button
            size="sm"
            className="w-full sm:w-auto"
            onClick={handleShowFormToggle}
          >
            {!showForm && <UserRoundPlus className="h-4 w-4" />}{" "}
            {showForm ? "Cancel" : "Add Customer"}
          </Button>
        </div>
      )}

      {showForm ? (
        <div className="mb-6 p-2 sm:p-0">
          <ISPCustomerForm />
        </div>
      ) : selectedCustomer ? (
        <div>
          <CustomerView
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={handleBackToList}
          />
        </div>
      ) : (
        <>
          <div className="w-full mb-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "All"
                    ? "border-blue-500 ring-2 ring-blue-200"
                    : "border-blue-200 hover:border-blue-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("All")}
              >
                <h3 className="text-xs font-medium text-blue-700 mb-0.5">
                  Total Customers
                </h3>
                <p className="text-xl font-bold text-blue-800">
                  {customers.length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Active"
                    ? "border-green-500 ring-2 ring-green-200"
                    : "border-green-200 hover:border-green-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Active")}
              >
                <h3 className="text-xs font-medium text-green-700 mb-0.5">
                  Active Customers
                </h3>
                <p className="text-xl font-bold text-green-800">
                  {customers.filter((c) => c.status === "Active").length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Inactive"
                    ? "border-yellow-500 ring-2 ring-yellow-200"
                    : "border-yellow-200 hover:border-yellow-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Inactive")}
              >
                <h3 className="text-xs font-medium text-yellow-700 mb-0.5">
                  Inactive Customers
                </h3>
                <p className="text-xl font-bold text-yellow-800">
                  {customers.filter((c) => c.status === "Inactive").length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Expired"
                    ? "border-red-500 ring-2 ring-red-200"
                    : "border-red-200 hover:border-red-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Expired")}
              >
                <h3 className="text-xs font-medium text-red-700 mb-0.5">
                  Expired Customers
                </h3>
                <p className="text-xl font-bold text-red-800">
                  {customers.filter((c) => c.status === "Expired").length}
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2 text-sm">
              <span>Show</span>
              <select
                value={selectedItemsPerPageValue}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="15">15</option>
                <option value="30">30</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="all">All</option>
              </select>
              <span>entries</span>
            </div>
          </div>

          <div className="w-full overflow-x-auto rounded shadow-sm">
            <table className="min-w-max w-full text-xs sm:text-[12px]">
              <thead className="bg-gray-100 text-left">
                <tr>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                    SN
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                    Status
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                    Username
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                    Name
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                    Mobile
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden sm:table-cell">
                    Address
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                    Valid Until
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                    Package
                  </th>
                  <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody>
                {currentCustomers.map((customer, index) => (
                  <tr
                    key={customer.id}
                    className={`hover:bg-gray-50 ${
                      selectedCustomer?.id === customer.id ? "bg-blue-50" : ""
                    }`}
                  >
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-center">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                      {customer.status === "Active" && (
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                          Active
                        </span>
                      )}
                      {customer.status === "Inactive" && (
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-yellow-100 text-yellow-800">
                          Inactive
                        </span>
                      )}
                      {customer.status === "Expired" && (
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                          Expired
                        </span>
                      )}
                    </td>
                    <td
                      className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-blue-600 hover:underline cursor-pointer"
                      onClick={() => handleSelectCustomer(customer)}>{customer.username}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                      {customer.name}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                      {customer.mobile}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden sm:table-cell">
                      {customer.address}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                      {customer.expiration}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 truncate hidden md:table-cell">
                      {customer.package}
                    </td>
                    <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-center hidden md:table-cell">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-7 w-7"
                        onClick={() => handleSelectCustomer(customer)}
                      >
                        <SquarePen className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-1 justify-left mt-4">
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-[12px]">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() =>
                  setCurrentPage((p) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
