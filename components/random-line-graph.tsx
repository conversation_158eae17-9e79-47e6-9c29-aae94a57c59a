"use client";

import React, { useState, useEffect } from 'react'; // Import useEffect
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';

// Define the type for your data points
interface GraphDataPoint {
  name: string;
  pv: number; // Placeholder for Download data
  uv: number; // Placeholder for Upload data
}

// Define the props for RandomLineGraph
interface RandomLineGraphProps {
  timeRange: string; // e.g., "1day", "1month", "1year"
}

// Function to generate random data based on the number of points
const generateRandomData = (numPoints: number): GraphDataPoint[] => {
  const data: GraphDataPoint[] = [];
  for (let i = 0; i < numPoints; i++) {
    // Generate data for downloads (pv) and uploads (uv)
    data.push({
      name: `Pt ${i + 1}`, // Generic point name for now
      pv: Math.floor(Math.random() * 500) + 50, // Example: 50-550 units (e.g., Mbps, GB)
      uv: Math.floor(Math.random() * 200) + 20,  // Example: 20-220 units (e.g., Mbps, GB)
    });
  }
  return data;
};

const RandomLineGraph: React.FC<RandomLineGraphProps> = ({ timeRange }) => {
  const [graphData, setGraphData] = useState<GraphDataPoint[]>([]);

  useEffect(() => {
    let numPoints = 0;
    let xAxisLabel = '';

    switch (timeRange) {
      case '1day':
        numPoints = 24; // 24 hours in a day
        xAxisLabel = 'Hour';
        break;
      case '3days':
        numPoints = 72; // 3 days * 24 hours
        xAxisLabel = 'Hour';
        break;
      case '7days':
        numPoints = 7; // Daily points for a week
        xAxisLabel = 'Day';
        break;
      case '15days':
        numPoints = 15; // Daily points
        xAxisLabel = 'Day';
        break;
      case '1month':
        numPoints = 30; // ~30 days in a month
        xAxisLabel = 'Day';
        break;
      case '3months':
        numPoints = 90; // ~90 days
        xAxisLabel = 'Day';
        break;
      case '6months':
        numPoints = 6; // Monthly points
        xAxisLabel = 'Month';
        break;
      case '1year':
        numPoints = 12; // Monthly points for a year
        xAxisLabel = 'Month';
        break;
      default:
        numPoints = 30; // Default to 1 month
        xAxisLabel = 'Day';
    }

    const newData = generateRandomData(numPoints);
    // Adjust 'name' based on xAxisLabel for better display
    const formattedData = newData.map((point, index) => {
      let name;
      if (xAxisLabel === 'Hour') {
        name = `${index}`; // 0-23 for hours
      } else if (xAxisLabel === 'Day') {
        name = `Day ${index + 1}`;
      } else if (xAxisLabel === 'Month') {
        name = `Month ${index + 1}`;
      }
      return { ...point, name };
    });

    setGraphData(formattedData);
  }, [timeRange]); // Re-generate data whenever timeRange changes

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart
        data={graphData} // Use graphData from state
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" label={{ value: timeRange === '1day' || timeRange === '3days' ? 'Hour of Day' : 'Time Period', position: 'insideBottom', offset: 0 }} />
        <YAxis label={{ value: 'Usage (Units)', angle: -90, position: 'insideLeft' }} />
        <Tooltip />
        <Legend />
        <Line type="monotone" dataKey="pv" name="Download" stroke="#8884d8" activeDot={{ r: 8 }} />
        <Line type="monotone" dataKey="uv" name="Upload" stroke="#82ca9d" />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default RandomLineGraph;