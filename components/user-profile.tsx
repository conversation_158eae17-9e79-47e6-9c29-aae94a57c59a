"use client"

import { useState } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function UserProfile() {
  const [user] = useState({
    name: "<PERSON> <PERSON><PERSON>",
    username: "leomin",
    email: "<EMAIL>",
    mobile: "9800000000",
    profilePic: "/images/leomin.jpeg"
  })

  return (
    <div className="p-5">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 bg-white p-6 rounded-lg shadow-md ">
        {/* Left: Profile Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">👤 My Profile</h2>

          {/* Profile Picture */}
          <div className="flex items-center mb-6">
            <Image
              src={user.profilePic}
              alt="Profile"
              width={60}
              height={60}
              className="rounded-md object-cover mr-3"
            />
            <Button variant="secondary" size="sm">Change Picture</Button>
          </div>

          {/* Personal Info */}
          <div className="space-y-3 mr-10">
            <div>
              <label className="block text-sm text-gray-700">Name</label>
              <Input value={user.name} disabled />
            </div>
            <div>
              <label className="block text-sm text-gray-700">Username</label>
              <Input value={user.username} disabled />
            </div>
            <div>
              <label className="block text-sm text-gray-700">Email</label>
              <Input value={user.email} disabled />
            </div>
            <div>
              <label className="block text-sm text-gray-700">Mobile Number</label>
              <Input value={user.mobile} disabled />
            </div>
          </div>
        </div>

        {/* Right: Change Password */}
        <div>
          <h2 className="text-2xl font-bold mb-6">🔐 Change Password</h2>
          <div className="space-y-3 mr-10">
            <div>
              <label className="block text-sm text-gray-700">New Password</label>
              <Input type="password" placeholder="Enter new password" />
            </div>
            <div>
              <label className="block text-sm text-gray-700">Confirm Password</label>
              <Input type="password" placeholder="Confirm new password" />
            </div>
            <Button className="mt-4" size="sm">Update Password</Button>
          </div>
        </div>
      </div>
    </div>
  )
}
