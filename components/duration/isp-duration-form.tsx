"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

export default function durationAddForm() {
  const [form, setForm] = useState({
    duration: "",
  })



  const handleChange = (field: keyof typeof form, value: string) => {
    setForm({ ...form, [field]: value })
  }

  const handleSubmit = () => {
    console.log("Submitted:", form)
    // You can post this data to an API or store it in a database
  }

  return (
    <div className="bg-white p-6 rounded shadow  mx-auto space-y-6">
      <h2 className="text-xl font-bold">Add duration</h2>

      {/* Dropdown Fields */}
      <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <Label>Duration</Label>
                <Input type="text" value={form.firstName} onChange={(e) => handleChange("packagename", e.target.value)}/>
        </div>
      </div>
        

      <div>
        <Button onClick={handleSubmit}>Submit</Button>
      </div>
    </div>
  )
}