"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import  ISPdurationForm from "@/components/duration/isp-duration-form"

export default function DurationDetails() {
  const [showForm, setShowForm] = useState(false)

  // Sample duration list (Replace with API data later)
  const duration = [
    {
      id: 1,
      duration: "1 month",
    },
    {
      id: 2,
      duration: "3 month",
    },
    {
      id: 3,
      duration: "6 month",
    },
    {
      id: 4,
      duration: "12 month",
    },
  ]

  return (
    <div className="p-5  mx-auto bg-white rounded-lg">
      <div className="flex justify-between items-center  mb-6">
        <h1 className="text-xl font-bold">Duration Details</h1>
        <Button size="sm" onClick={() => setShowForm(!showForm)}>
          {showForm ? "Cancel" : "Add Duration"}
        </Button>
      </div>

      {showForm ? (
        <div className="mb-6">
          <ISPdurationForm />
        </div>
      ) : (
        <div className="overflow-x-auto rounded">
          <table className="min-w-full border text-[13px]">
            <thead className="bg-gray-100 text-left">
              <tr>
                <th className="border px-4 py-2">SN</th>
                <th className="border px-4 py-2">Duration</th>
                <th className="border px-4 py-2">Action</th>
              </tr>
            </thead>
            <tbody>
              {duration.map((duration, index) => (
                <tr key={duration.id} className="hover:bg-gray-50">
                  <td className="border px-4 py-2">{index + 1}</td>
                  <td className="border px-4 py-2">{duration.duration}</td>
                  <td className="border px-4 py-2 w-1/6">
                    <Button variant="outline" size="ss">Edit</Button>
                    <Button variant="outline" size="ss">Delete</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}