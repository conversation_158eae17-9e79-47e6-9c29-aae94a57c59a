// components/organization-management.tsx
"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export interface organization {
  id: number;
  organizationname: string;
}

// organization Table Component
export function OrganizationTable({
  organizations,
  onDelete,
  onEdit,
}: {
  organizations: organization[];
  onDelete: (id: number) => void;
  onEdit: (organization: organization) => void;
}) {
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Organization Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {organizations.map((organization) => (
              <OrganizationRow
                key={organization.id}
                organization={organization}
                onDelete={onDelete}
                onEdit={onEdit}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// organization Row Component
function OrganizationRow({
  organization,
  onDelete,
  onEdit,
}: {
  organization: organization;
  onDelete: (id: number) => void;
  onEdit: (organization: organization) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <>
      <TableRow>
        <TableCell>{organization.id}</TableCell>
        <TableCell>{organization.organizationname}</TableCell>
        <TableCell className="text-right space-x-2">
          <Button
            variant="outline"
            size="ss"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="ss"
            onClick={() => onDelete(organization.id)}
          >
            Delete
          </Button>
        </TableCell>
      </TableRow>

      {isEditing && (
        <EditorganizationModal
          organization={organization}
          onClose={() => setIsEditing(false)}
          onSave={(updatedorganization) => {
            onEdit(updatedorganization);
            setIsEditing(false);
          }}
        />
      )}
    </>
  );
}

// // Add organization Form Component
export function AddOrganizationForm({
  onSubmit,
}: {
  onSubmit: (organization: organization) => void;
}) {
  const [neworganization, setNeworganization] = useState({
    organizationname: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...neworganization,
          organizationname: neworganization.organizationname.trim(),
        });
        setNeworganization({
          organizationname: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="organizationname"
        value={neworganization.organizationname}
        onChange={(e) =>
          setNeworganization((prev) => ({ ...prev, organizationname: e.target.value }))
        }
      />

      <Button type="submit" className="w-full">
        Add organization
      </Button>
    </form>
  );
}

// Edit organization Modal Component
function EditorganizationModal({
  organization,
  onClose,
  onSave,
}: {
  organization: organization;
  onClose: () => void;
  onSave: (organization: organization) => void;
}) {
  const [editedorganization, setEditedorganization] = useState(organization);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white p-6 rounded-lg space-y-4 max-w-md w-full">
        <h2 className="text-xl font-bold">Edit organization</h2>

        <Input
          value={editedorganization.organizationname}
          onChange={(e) =>
            setEditedorganization((prev) => ({ ...prev, organizationname: e.target.value }))
          }
        />

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={() =>
              onSave({
                ...editedorganization,
              })
            }
          >
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
}
