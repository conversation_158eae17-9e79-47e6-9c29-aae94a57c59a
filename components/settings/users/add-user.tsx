"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export type UserRole = "user" | "admin" | "superadmin" | "sales" | "support";

export interface User {
  id: number;
  username: string;
  role: UserRole;
  password: string;
}

export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  const [newUser, setNewUser] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    role: "user" as UserRole,
  });

  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false,
  });

  const handlePasswordChange = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
    });
    setNewUser((prev) => ({ ...prev, password }));
  };

  const isValid =
    newUser.username.trim() !== "" &&
    passwordStrength.length &&
    passwordStrength.uppercase &&
    passwordStrength.number &&
    newUser.password === newUser.confirmPassword;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newUser,
          username: newUser.username.trim(),
        });
        setNewUser({
          username: "",
          password: "",
          confirmPassword: "",
          role: "user",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Username"
        value={newUser.username}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, username: e.target.value }))
        }
      />

      <PasswordInput
        value={newUser.password}
        onChange={handlePasswordChange}
        strength={passwordStrength}
      />

      <Input
        type="password"
        placeholder="Confirm Password"
        value={newUser.confirmPassword}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
        }
      />

      <RoleSelect
        value={newUser.role}
        onChange={(role) => setNewUser((prev) => ({ ...prev, role }))}
      />

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      <div className="flex gap-2 text-sm">
        <span className={strength.length ? "text-green-600" : "text-gray-500"}>
          • 8+ characters
        </span>
        <span
          className={strength.uppercase ? "text-green-600" : "text-gray-500"}
        >
          • Uppercase
        </span>
        <span className={strength.number ? "text-green-600" : "text-gray-500"}>
          • Number
        </span>
      </div>
    </div>
  );
}

function RoleSelect({
  value,
  onChange,
}: {
  value: UserRole;
  onChange: (role: UserRole) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value as UserRole)}
    >
      <option value="user">User</option>
      <option value="admin">Admin</option>
      <option value="superadmin">Super Admin</option>
      <option value="sales">Sales</option>
      <option value="support">Support</option>
    </select>
  );
}
