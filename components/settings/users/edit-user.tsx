"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User, UserRole } from "./user-management";

export interface EditUserFormProps {
  user: User;
  onSubmit: (user: User) => void;
}

export function EditUserForm({ user, onSubmit }: EditUserFormProps) {
  const [editedUser, setEditedUser] = useState(user);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const passwordStrength = {
    length: newPassword.length >= 8,
    uppercase: /[A-Z]/.test(newPassword),
    number: /\d/.test(newPassword),
  };

  const isValid =
    editedUser.username.trim() !== "" &&
    (newPassword === "" ||
      (passwordStrength.length &&
        passwordStrength.uppercase &&
        passwordStrength.number &&
        newPassword === confirmPassword));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...editedUser,
      password: newPassword || editedUser.password,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Username</label>
        <Input
          value={editedUser.username}
          onChange={(e) =>
            setEditedUser((prev) => ({ ...prev, username: e.target.value }))
          }
          placeholder="Username"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">New Password</label>
        <PasswordInput
          value={newPassword}
          onChange={(password) => setNewPassword(password)}
          strength={passwordStrength}
          placeholder="New Password (leave blank to keep current)"
        />
      </div>

      {newPassword && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Confirm New Password</label>
          <Input
            type="password"
            placeholder="Confirm New Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
          />
        </div>
      )}

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Role</label>
        <RoleSelect
          value={editedUser.role}
          onChange={(role) => setEditedUser((prev) => ({ ...prev, role }))}
        />
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      {value && (
        <div className="flex gap-2 text-sm">
          <span className={strength.length ? "text-green-600" : "text-gray-500"}>
            • 8+ characters
          </span>
          <span
            className={strength.uppercase ? "text-green-600" : "text-gray-500"}
          >
            • Uppercase
          </span>
          <span className={strength.number ? "text-green-600" : "text-gray-500"}>
            • Number
          </span>
        </div>
      )}
    </div>
  );
}

function RoleSelect({
  value,
  onChange,
}: {
  value: UserRole;
  onChange: (role: UserRole) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value as UserRole)}
    >
      <option value="user">User</option>
      <option value="admin">Admin</option>
      <option value="superadmin">Super Admin</option>
      <option value="sales">Sales</option>
      <option value="support">Support</option>
    </select>
  );
}
