// components/group-management.tsx
"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export interface group {
  id: number;
  groupname: string;
}

// group Table Component
export function GroupTable({
  groups,
  onDelete,
  onEdit,
}: {
  groups: group[];
  onDelete: (id: number) => void;
  onEdit: (group: group) => void;
}) {
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Group Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {groups.map((group) => (
              <GroupRow
                key={group.id}
                group={group}
                onDelete={onDelete}
                onEdit={onEdit}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// group Row Component
function GroupRow({
  group,
  onDelete,
  onEdit,
}: {
  group: group;
  onDelete: (id: number) => void;
  onEdit: (group: group) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <>
      <TableRow>
        <TableCell>{group.id}</TableCell>
        <TableCell>{group.groupname}</TableCell>
        <TableCell className="text-right space-x-2">
          <Button
            variant="outline"
            size="ss"
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="ss"
            onClick={() => onDelete(group.id)}
          >
            Delete
          </Button>
        </TableCell>
      </TableRow>

      {isEditing && (
        <EditgroupModal
          group={group}
          onClose={() => setIsEditing(false)}
          onSave={(updatedgroup) => {
            onEdit(updatedgroup);
            setIsEditing(false);
          }}
        />
      )}
    </>
  );
}

// // Add group Form Component
export function AddGroupForm({
  onSubmit,
}: {
  onSubmit: (group: group) => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newgroup,
          groupname: newgroup.groupname.trim(),
        });
        setNewgroup({
          groupname: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Group Name"
        value={newgroup.groupname}
        onChange={(e) =>
          setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
        }
      />

      <Button type="submit" className="w-full">
        Add group
      </Button>
    </form>
  );
}

// Edit group Modal Component
function EditgroupModal({
  group,
  onClose,
  onSave,
}: {
  group: group;
  onClose: () => void;
  onSave: (group: group) => void;
}) {
  const [editedgroup, setEditedgroup] = useState(group);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white p-6 rounded-lg space-y-4 max-w-md w-full">
        <h2 className="text-xl font-bold">Edit group</h2>

        <Input
          value={editedgroup.groupname}
          onChange={(e) =>
            setEditedgroup((prev) => ({ ...prev, groupname: e.target.value }))
          }
        />

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={() =>
              onSave({
                ...editedgroup,
              })
            }
          >
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
}
