"use client";

import { useState, useEffect } from "react";
import {
  usageData,
  type CustomerType,
  type UsageRecord,
} from "@/app/data/customers";
import { Button } from "@/components/ui/button";
import RandomLineGraph from "./random-line-graph";
import { Plus, SquarePen, ChevronLeft, ChevronRight, UserRoundPlus, UserRoundPen, ListRestart } from "lucide-react";


const TABS = [
  "Customer Details",
  "Router Info",
  // "Tickets",
  // "Subscription",
  "Usage",
  "Graph",
] as const;

type Props = {
  selectedCustomer: CustomerType;
  setSelectedCustomer: (c: CustomerType | null) => void;
};

export default function CustomerView({
  selectedCustomer,
  setSelectedCustomer,
}: Props) {
  // --- Calculate Default Dates ---
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(today.getDate()).padStart(2, "0");

  const defaultStartDate = `${year}-${month}-01`;
  const defaultEndDate = `${year}-${month}-${day}`;

  const [activeTab, setActiveTab] = useState<(typeof TABS)[number]>(TABS[0]);
  const [startDate, setStartDate] = useState<string>(defaultStartDate);
  const [endDate, setEndDate] = useState<string>(defaultEndDate);
  const [appliedStartDate, setAppliedStartDate] =
    useState<string>(defaultStartDate);
  const [appliedEndDate, setAppliedEndDate] = useState<string>(defaultEndDate);

  const [selectedGraphRange, setSelectedGraphRange] = useState<string>("1day");
  // State for the image popup (modal)
  const [showImagePopup, setShowImagePopup] = useState(false);
  const [currentImageSrc, setCurrentImageSrc] = useState<string | null>(null);

  useEffect(() => {
    setStartDate(defaultStartDate);
    setEndDate(defaultEndDate);
    setAppliedStartDate(defaultStartDate);
    setAppliedEndDate(defaultEndDate);
  }, [selectedCustomer.id, activeTab]);

  // Filter usage data for the selected customer
  const customerUsageData = usageData.filter(
    (record) => record.customerId === selectedCustomer.id
  );

  // Sort and get the last 3 usage records for the "Customer Details" section
  const lastThreeUsageRecords = [...customerUsageData] // Create a shallow copy to avoid mutating original array
    .sort(
      (a, b) =>
        new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    ) // Sort by startTime descending
    .slice(0, 3); // Get the first 3 (which are the latest)

  // Filter the full usage data by date range for the "Usage" tab
  const filteredUsageByDate = customerUsageData.filter((record) => {
    const recordStartTimestamp = new Date(record.startTime).getTime();
    const recordEndTimestamp = new Date(record.endTime).getTime();

    const filterStartTimestamp = appliedStartDate
      ? new Date(appliedStartDate + "T00:00:00").getTime()
      : null;
    const filterEndTimestamp = appliedEndDate
      ? new Date(appliedEndDate + "T23:59:59").getTime() // Use endDate from input for filtering on submit
      : null;

    let passesFilter = true;

    if (
      filterStartTimestamp !== null &&
      recordEndTimestamp < filterStartTimestamp
    ) {
      passesFilter = false;
    }

    if (
      filterEndTimestamp !== null &&
      recordStartTimestamp > filterEndTimestamp
    ) {
      passesFilter = false;
    }

    return passesFilter;
  });

  const handleDateSubmit = () => {
    setAppliedStartDate(startDate);
    setAppliedEndDate(endDate);
    console.log("Applying usage data filter for:", startDate, "to", endDate);
  };

  const handleImageClick = (src: string) => {
    setCurrentImageSrc(src);
    setShowImagePopup(true);
  };

  const closeImagePopup = () => {
    setShowImagePopup(false);
    setCurrentImageSrc(null);
  };

  const graphRanges = [
    { label: "1 Day", value: "1day" },
    { label: "3 Days", value: "3days" },
    { label: "7 Days", value: "7days" },
    { label: "15 Days", value: "15days" },
    { label: "1 Month", value: "1month" },
    { label: "3 Months", value: "3months" },
    { label: "6 Months", value: "6months" },
    { label: "1 Year", value: "1year" },
  ];

  return (
    <div className="mb-5 space-y-4">
      {" "}
      {/* OUTERMOST DIV START */}
      {/* --- Header bar with name / status / address --- */}
      <div className="grid grid-cols-1 md:grid-cols-3 items-center p-2 rounded-lg  text-sm gap-2">
        <div className="font-semibold text-center md:text-left">
          {selectedCustomer.name}
        </div>
        <div className="text-center">
          Status:{" "}
          <span
            className={`font-medium ${
              selectedCustomer.status === "Active"
                ? "text-green-600"
                : selectedCustomer.status === "Inactive"
                ? "text-yellow-600"
                : "text-red-600"
            }`}
          >
            {selectedCustomer.status}
          </span>
        </div>
        <div className="text-center md:text-right text-gray-700">
          Address: {selectedCustomer.address}
        </div>
      </div>{" "}
      {/* HEADER BAR DIV END */}
      {/* --- Tabs bar --- */}
      <div className="flex flex-wrap justify-between text-center bg-gray-50 border-b rounded-lg border-gray-300">
        {TABS.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 min-w-[140px] py-2 text-sm ${
              activeTab === tab
                ? "border-b-2 border-blue-600 text-blue-600 font-medium"
                : "text-gray-700 hover:text-gray-900"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>{" "}
      {/* TABS BAR DIV END */}
      {/* --- Content area based on activeTab --- */}
      <div>
        {activeTab === "Customer Details" && (
          <div className="grid grid-cols-1 gap-4">
            {/* This outer grid for the entire tab */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Basic Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Basic Info
                </h3>
                {/* Add overflow-x-auto to make the table horizontally scrollable on small screens */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Username
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.username}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Full Name
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.name}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Mobile
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.mobile}
                        </td>
                      </tr>
                      {selectedCustomer.altMobile && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Alt. Mobile
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.altMobile}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.email && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Email
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.email}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.nationalId && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            National ID
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nationalId}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.dateOfBirth && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            DOB
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.dateOfBirth}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Registered
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.registered}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              {/* Service & Package Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Service & Package Info
                </h3>
                {/* Wrap the table in overflow-x-auto for responsiveness */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Package
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.package}
                        </td>
                      </tr>
                      {selectedCustomer.bandwidthDownload && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Download
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.bandwidthDownload}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.bandwidthUpload && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Upload
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.bandwidthUpload}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Expiration
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.expiration}
                        </td>
                      </tr>
                      {selectedCustomer.contractType && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Contract
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.contractType}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.lastPaymentDate && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Last Paid
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.lastPaymentDate}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.nextBillDueDate && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Next Bill
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nextBillDueDate}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.outstandingBalance && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Outstanding
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.outstandingBalance}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
              {/* Connection & Address Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Connection & Address Info
                </h3>
                {/* Wrap the table in overflow-x-auto for horizontal scrolling on small screens */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Service Address
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.address}
                        </td>
                      </tr>
                      {selectedCustomer.landmark && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Landmark
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.landmark}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.ip && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Assigned IP
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.ip}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.mac && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            CPE MAC
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.mac}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.nasIp && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            NAS IP
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nasIp}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Session Status:
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap">
                          {/* First, check if the customer's overall status is "Expired" */}
                          {selectedCustomer.status === "Expired" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                              Expired
                            </span>
                          ) : // If not "Expired", then proceed with checking the actual sessionStatus
                          selectedCustomer.sessionStatus === "Online" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                              Online
                            </span>
                          ) : selectedCustomer.sessionStatus === "Offline" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                              Offline
                            </span>
                          ) : (
                            // Fallback for any other sessionStatus value (e.g., null, undefined, or other string)
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-gray-100 text-gray-800">
                              {selectedCustomer.sessionStatus || "-"}
                            </span>
                          )}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Online Duration
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.onlineDuration || "-"}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            {/* --- Last 3 Usage Sessions (Table Format) --- */}
            <div className="bg-white p-4 rounded-lg border  shadow-sm text-xs overflow-x-auto">
              <h3 className="font-semibold mb-3 text-sm">Last 3 Sessions</h3>
              {lastThreeUsageRecords.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Start Time
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        End Time
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Session
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Download
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Upload
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        IP Address
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        MAC Address
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {lastThreeUsageRecords.map((record, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.startTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.endTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.sessionTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.download}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.upload}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.ipAddress}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.macAddress}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No recent usage sessions found.</p>
              )}
            </div>
          </div>
        )}
        {/* ---Router Section--- */}
        {activeTab === "Router Info" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* ONT/Device Status */}
            <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
              <h3 className="font-semibold mb-2 text-sm">ONT/Device Status</h3>
              <p>
                <strong>Status:</strong>{" "}
                <span
                  className={`font-medium ${
                    selectedCustomer.routerInfo?.ontStatus === "Online"
                      ? "text-green-600"
                      : selectedCustomer.routerInfo?.ontStatus === "Offline"
                      ? "text-red-600"
                      : "text-yellow-600"
                  }`}
                >
                  {selectedCustomer.routerInfo?.ontStatus || "N/A"}
                </span>
              </p>
              <p>
                <strong>Model:</strong>{" "}
                {selectedCustomer.routerInfo?.ontModel || "N/A"}
              </p>
              <p>
                <strong>Serial No:</strong>{" "}
                {selectedCustomer.routerInfo?.ontSerial || "N/A"}
              </p>
              <p>
                <strong>MAC Address:</strong>{" "}
                {selectedCustomer.routerInfo?.ontMac || "N/A"}
              </p>
              <p>
                <strong>Firmware:</strong>{" "}
                {selectedCustomer.routerInfo?.firmwareVersion || "N/A"}
              </p>
            </div>

            {/* Optical Signal Levels */}
            <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
              <h3 className="font-semibold mb-2 text-sm">Optical Levels</h3>
              <p>
                <strong>Rx Power:</strong>{" "}
                {selectedCustomer.routerInfo?.rxPower || "N/A"}
              </p>
              <p>
                <strong>Tx Power:</strong>{" "}
                {selectedCustomer.routerInfo?.txPower || "N/A"}
              </p>
            </div>

            {/* Network Configuration */}
            <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
              <h3 className="font-semibold mb-2 text-sm">Network Config</h3>
              <p>
                <strong>WAN IP:</strong>{" "}
                {selectedCustomer.routerInfo?.wanIpAddress || "N/A"}
              </p>
              <p>
                <strong>Gateway:</strong>{" "}
                {selectedCustomer.routerInfo?.gatewayIp || "N/A"}
              </p>
              <p>
                <strong>Connection Type:</strong>{" "}
                {selectedCustomer.routerInfo?.connectionType || "N/A"}
              </p>
              <p>
                <strong>VLAN ID:</strong>{" "}
                {selectedCustomer.routerInfo?.vlanId || "N/A"}
              </p>
            </div>

            {/* Wi-Fi Router Info */}
            <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
              
              <div className="flex flex-col sm:flex-row sm:justify-between items-start sm:items-left gap-4">
                <div>
                <h3 className="font-semibold mb-2 text-sm">Wi-Fi Router Info</h3>
                <div className="flex flex-col justify-start items-start">
                  <p className="mb-1">
                    <strong>SSID:</strong>{" "}
                    {selectedCustomer.routerInfo?.wifiSsid || "N/A"}
                  </p>
                  <p>
                    <strong>MAC Address:</strong>{" "}
                    {selectedCustomer.routerInfo?.wifiMac || "N/A"}
                  </p>
                </div>
                </div>

                <div className="flex flex-col justify-center  items-center text-center"> 
                  <h3 className="font-semibold mb-2 text-sm">Customer ONU</h3>
                  {selectedCustomer.routerInfo?.onuImg ? (
                     <img
          src={selectedCustomer.routerInfo.onuImg} // Dynamically pull image source
          alt="ONU Device"
          className="max-w-[100px] h-auto rounded-md shadow-md  cursor-pointer"
          // Ensure a string is always passed, even if empty, to handleImageClick
          onClick={() => handleImageClick(selectedCustomer.routerInfo?.onuImg || "")}
        />
                  ) : (
                    // Placeholder icon when no ONU image path is available
                   <span className="text-gray-500 items-center text-sm">ONU image not found</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        {/* {activeTab === "Tickets" && <div>— Tickets content —</div>}
        {activeTab === "Subscription" && <div>— Subscription content —</div>} */}
        {/* --- Usage Section with Table --- */}
        {activeTab === "Usage" && (
          <div className="space-y-4">
            {/* Date Range Selector */}
            <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-start space-y-2 sm:space-y-0 sm:space-x-4 bg-white p-0 rounded-lg shadow-sm">
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="p-2 border h-8 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-auto text-sm"
                />
                <span className="flex items-center justify-center sm:justify-start text-gray-700">
                  -
                </span>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="p-2 border h-8 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-auto text-sm"
                />
              </div>
              <Button
                size="sm"
                onClick={handleDateSubmit}
                className="px-4 py-2 w-full sm:w-auto text-sm"
              >
                Submit
              </Button>
            </div>

            {/* Usage Table */}
            <div className="overflow-x-auto bg-white rounded-lg shadow-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Start Time
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      End Time
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Session Time
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Upload
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Download
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      IP Address
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      MAC Address
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Service Type
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsageByDate.length > 0 ? (
                    filteredUsageByDate.map((row, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.startTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.endTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.sessionTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.upload}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.download}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.ipAddress}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.macAddress}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {row.serviceType}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={8}
                        className="px-3 py-4 text-center text-gray-500"
                      >
                        No usage data found for the selected customer or date
                        range.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}{" "}
        {/* USAGE SECTION DIV END */}
        {/* --- Graph Section with Range Selector --- */}
        {activeTab === "Graph" && (
          <div className="bg-white shadow-md rounded-lg p-5 space-y-4">
            <h3 className="text-lg font-semibold">Usage Graph</h3>
            {/* Graph Range Selector Buttons */}
            <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
              {graphRanges.map((range) => (
                <Button
                  key={range.value}
                  size="sm"
                  variant={
                    selectedGraphRange === range.value ? "default" : "outline"
                  }
                  onClick={() => setSelectedGraphRange(range.value)}
                  className="text-xs"
                >
                  {range.label}
                </Button>
              ))}
            </div>
            {/* Render the RandomLineGraph component, passing the selected range */}
            <RandomLineGraph timeRange={selectedGraphRange} />
          </div>
        )}{" "}
        {/* GRAPH SECTION DIV END */}
      </div>{" "}
      {/* CONTENT AREA DIV END */}
      {/* --- Back & Edit buttons --- */}
      <div className="flex flex-col sm:flex-row gap-2 mt-4 justify-end">
        <Button
          size="sm"
          onClick={() => setSelectedCustomer(null)}
          className="w-full sm:w-auto"><ListRestart className="h-4 w-4" />
          Back to List 
        </Button>
        <Button size="sm" variant="outline" className="w-full sm:w-auto"><UserRoundPen className="h-4 w-4" />
           Customer
        </Button>
      </div>{" "}
      {/* BUTTONS DIV END */}
      {/* --- Image Popup (Modal) --- */}
      {showImagePopup && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex justify-center items-center z-50 p-4"
          onClick={closeImagePopup} // Close when clicking outside the image
        >
          <div className="relative" onClick={(e) => e.stopPropagation()}>
            {" "}
            {/* Prevent closing when clicking on the image container */}
            <img
              src={currentImageSrc || ""} // Ensure src is never null/undefined for the modal
              alt="Full view"
              className="max-h-[90vh] max-w-[90vw] object-contain rounded-lg shadow-xl"
            />
            <button
              className="absolute top-2 right-2 text-white text-3xl font-bold bg-gray-800 rounded-full w-8 h-8 flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors duration-200"
              onClick={closeImagePopup}
              aria-label="Close"
            >
              &times;
            </button>
          </div>
        </div>
      )}
    </div> /* OUTERMOST DIV END */
  );
}
