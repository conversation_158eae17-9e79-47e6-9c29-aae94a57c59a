// app/login/layout.tsx
import RootLayout, { commonMetadata } from "../root-layout"; // Import the common RootLayout and metadata
import type React from "react";
import type { Metadata } from "next"; // Make sure Metadata is imported

// Merge common metadata with any login-specific metadata (optional)
export const metadata: Metadata = {
  ...commonMetadata, // Spread common metadata
  title: "Login | Workalaya", // Override title for the login page
};

export default function LoginLayout({ children }: { children: React.ReactNode }) {
  return (
    <RootLayout> {/* Use the common RootLayout */}
      {children}
    </RootLayout>
  );
}