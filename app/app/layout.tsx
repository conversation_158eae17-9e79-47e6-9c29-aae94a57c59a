// app/app/layout.tsx
import RootLayout, { commonMetadata } from "../root-layout"; // Import the common RootLayout and metadata
import Template from "@/components/template";
import type React from "react";
import type { Metadata } from "next"; // Make sure <PERSON>ada<PERSON> is imported

// Merge common metadata with any app/dashboard-specific metadata (optional)
export const metadata: Metadata = {
  ...commonMetadata, // Spread common metadata
  title: {
    default: "Dashboard", // Keep specific default for dashboard
    template: "%s | Workalaya",
  },
  // description, icons etc. are inherited from commonMetadata unless overridden
};

export default function AppRootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <RootLayout children={undefined}> {/* Use the common RootLayout */}
      <Template children={undefined}> {/* The Template (sidebar, header) is specific to the app */}
        {children}
      </Template>
    </RootLayout>
  );
}