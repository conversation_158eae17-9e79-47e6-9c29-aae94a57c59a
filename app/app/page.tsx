export const metadata = {
  title: "Workalaya",
  description: "Workalaya",
};

import {
  LayoutGrid,
  Users,
  Ticket,
  BarChart3,
  Bell,
  Activity,
  TrendingUp,
  DollarSign,
} from "lucide-react";

export default function DashboardPage() {
  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Dashboard Content */}
      <main className=" flex-1 p-3 overflow-auto">
        {/* Client Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Total clients</h3>
          </div>
          <div className="bg-[#9b6ad9] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Active clients</h3>
          </div>
          <div className="bg-[#d16cb9] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Inactive clients</h3>
          </div>
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Expired clients</h3>
          </div>
        </div>

        {/* Metric Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Revenue</h3>
          </div>
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Invoices</h3>
          </div>
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Pending</h3>
          </div>
          <div className="bg-[#4a81b8] text-white p-4 rounded">
            <h3 className="text-sm font-medium">Overdue</h3>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Revenue Chart</span>
          </div>
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
        </div>

        <div className="grid grid-cols-1  gap-6 mb-6 ">
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
        </div>
        <div className="grid grid-cols-1  gap-6  ">
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <Activity className="text-[#4a90c7] h-6 w-6" />
              <div>
                <div className="text-xl font-bold">1,200</div>
                <div className="text-sm text-gray-500">Total Clients</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <TrendingUp className="text-green-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">800</div>
                <div className="text-sm text-gray-500">Active Clients</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <DollarSign className="text-yellow-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">$45,000</div>
                <div className="text-sm text-gray-500">Monthly Revenue</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <Ticket className="text-red-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">25</div>
                <div className="text-sm text-gray-500">Open Tickets</div>
              </div>
            </div>
          </div>

          {/* Statistics Cards 2*/}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <Users className="text-[#4a90c7] h-6 w-6" />
              <div>
                <div className="text-xl font-bold">1,200</div>
                <div className="text-sm text-gray-500">Total Clients</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <Bell className="text-green-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">800</div>
                <div className="text-sm text-gray-500">Active Clients</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <BarChart3 className="text-yellow-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">$45,000</div>
                <div className="text-sm text-gray-500">Monthly Revenue</div>
              </div>
            </div>
            <div className="bg-white p-4 rounded shadow flex items-center gap-4">
              <LayoutGrid className="text-red-500 h-6 w-6" />
              <div>
                <div className="text-xl font-bold">25</div>
                <div className="text-sm text-gray-500">Open Tickets</div>
              </div>
            </div>
          </div>

          {/* Main Panels */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            {/* Recent Activities */}
            <div className="bg-white p-4 rounded shadow col-span-2">
              <div className="font-bold mb-2">Recent Activities</div>
              <ul className="space-y-2">
                <li className="border-b pb-2 text-sm">
                  John Doe signed up (2 hrs ago)
                </li>
                <li className="border-b pb-2 text-sm">
                  Invoice #1234 paid (5 hrs ago)
                </li>
                <li className="border-b pb-2 text-sm">
                  Ticket #456 updated (1 day ago)
                </li>
                <li className="text-sm">New report generated (2 days ago)</li>
              </ul>
            </div>

            {/* Placeholder Chart Panel */}
            <div className="bg-white p-4 rounded shadow">
              <div className="font-bold mb-2">Revenue Chart</div>
              <div className="h-[180px] bg-gray-200 rounded flex items-center justify-center text-gray-500">
                Chart Placeholder
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="bg-white p-4 rounded shadow">
            <div className="font-bold mb-2">Client List</div>
            <table className="w-full text-sm">
              <thead>
                <tr className="text-left border-b">
                  <th className="py-2">Name</th>
                  <th className="py-2">Email</th>
                  <th className="py-2">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-2">John Doe</td>
                  <td className="py-2"><EMAIL></td>
                  <td className="py-2 text-green-600">Active</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2">Jane Smith</td>
                  <td className="py-2"><EMAIL></td>
                  <td className="py-2 text-yellow-500">Pending</td>
                </tr>
                <tr>
                  <td className="py-2">Bob Johnson</td>
                  <td className="py-2"><EMAIL></td>
                  <td className="py-2 text-red-500">Inactive</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  );
}
