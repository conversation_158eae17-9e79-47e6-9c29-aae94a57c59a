// page.tsx
"use client";
import { useState } from "react";
import {
  GroupTable,
  AddGroupForm,
  group,
} from "@/components/settings/group-management";
import { Button } from "@/components/ui/button";

export default function GroupManagementPage() {
  const [groups, setGroups] = useState<group[]>([
    { id: 1, groupname: "admin" },
    { id: 2, groupname: "superadmin" },
    { id: 3, groupname: "sales" },
    { id: 4, groupname: "support" },
  ]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [search, setSearch] = useState("");
  const filteredGroups = groups.filter((group) =>
    group.groupname.toLowerCase().includes(search.toLowerCase())
  );

  // add max-w-4xl ml later
  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Group Management Portal</h1>
        <input
          type="text"
          placeholder="Search groups."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button className="ml-20" onClick={() => setShowAddForm(true)}>
          Add New Group
        </Button>
      </div>

      <GroupTable
        groups={filteredGroups}
        onDelete={(id) => setGroups(groups.filter((group) => group.id !== id))}
        onEdit={(updatedGroup) =>
          setGroups(
            groups.map((group) =>
              group.id === updatedGroup.id ? updatedGroup : group
            )
          )
        }
      />

      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg space-y-4 max-w-md w-full">
            <h2 className="text-xl font-bold">Add New Group</h2>
            <AddGroupForm
              onSubmit={(newGroup) => {
                setGroups([...groups, newGroup]);
                setShowAddForm(false);
              }}
            />
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
