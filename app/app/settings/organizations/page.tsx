"use client";
import { useState } from "react";
import {
  OrganizationTable,
  AddOrganizationForm,
  organization,
} from "@/components/settings/organization-management";
import { Button } from "@/components/ui/button";

export default function OrganizationManagementPage() {
  const [organizations, setOrganizations] = useState<organization[]>([
    { id: 1, organizationname: "Malla Telecom" },
    { id: 2, organizationname: "Shrestha Telecom" },
    { id: 3, organizationname: "Bajra Telecom" },
    { id: 4, organizationname: "Kathmandu Telecom" },
  ]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [search, setSearch] = useState("");
  const filteredOrganizations = organizations.filter((organization) =>
    organization.organizationname.toLowerCase().includes(search.toLowerCase())
  );

  // add max-w-4xl ml later
  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Organization Management Portal</h1>
        <input
          type="text"
          placeholder="Search organizations."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button className="ml-20" onClick={() => setShowAddForm(true)}>
          Add New Organization
        </Button>
      </div>

      <OrganizationTable
        organizations={filteredOrganizations}
        onDelete={(id) => setOrganizations(organizations.filter((organization) => organization.id !== id))}
        onEdit={(updatedOrganization) =>
          setOrganizations(
            organizations.map((organization) =>
              organization.id === updatedOrganization.id ? updatedOrganization : organization
            )
          )
        }
      />

      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg space-y-4 max-w-md w-full">
            <h2 className="text-xl font-bold">Add New Organization</h2>
            <AddOrganizationForm
              onSubmit={(newOrganization) => {
                setOrganizations([...organizations, newOrganization]);
                setShowAddForm(false);
              }}
            />
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
