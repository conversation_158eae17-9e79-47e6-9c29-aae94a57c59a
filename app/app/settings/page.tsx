"use client";

import { useState, useEffect } from "react";

export default function SettingsPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch users on mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch("/api/users");

        // Check if response is empty or not JSON
        const text = await response.text();
        if (!text) {
          console.error("Empty response from server");
          return;
        }

        try {
          const data = JSON.parse(text);
          if (response.ok) setUsers(data);
        } catch (jsonError) {
          console.error("Invalid JSON response:", text, jsonError);
        }
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };
    fetchUsers();
  }, []);

  const handleAddUser = async (newUser: {
    username: string;
    password: string;
    role: string;
  }) => {
    try {
      const response = await fetch("/api/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newUser),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      const createdUser = await response.json();
      setUsers([...users, createdUser]);
    } catch (error) {
      alert(error instanceof Error ? error.message : "Failed to add user");
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      try {
        const response = await fetch(`/api/users?id=${userId}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error);
        }

        setUsers(users.filter((user) => user.id !== userId));
      } catch (error) {
        alert(error instanceof Error ? error.message : "Failed to delete user");
      }
    }
  };

  const handlePasswordChange = async (
    currentPassword: string,
    newPassword: string
  ) => {
    try {
      const response = await fetch("/api/auth/password", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ currentPassword, newPassword }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error);
      }

      return await response.json();
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : "Password change failed"
      );
    }
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-5 gap-4 mb-6">
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">35</div>
            <div className="text-sm text-gray-500">Total Users</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">3 / 35</div>
            <div className="text-sm text-gray-500">Super Admin</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">5 / 35</div>
            <div className="text-sm text-gray-500">Admin</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">20 / 35</div>
            <div className="text-sm text-gray-500">Support</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">17 / 35</div>
            <div className="text-sm text-gray-500">Sales</div>
          </div>
        </div>
      </div>
    </div>
  );
}
