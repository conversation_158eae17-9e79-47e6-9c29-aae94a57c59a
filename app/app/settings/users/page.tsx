"use client";

import { useState } from "react";
import { Link } from "next/link";
import {
  UserTable,
} from "@/components/settings/users/user-management";
import { Button } from "@/components/ui/button";
import { AddUserForm, User } from "@/components/settings/users/add-user";

export default function UserManagementPage() {
  // Generate 100 users
  const [users, setUsers] = useState<User[]>(
    Array.from({ length: 100 }, (_, i) => {
      const id = i + 1;
      const isAdmin = id % 10 === 0;
      return {
        id,
        username: isAdmin ? `admin${id}` : `user${id}`,
        role: isAdmin ? "admin" : "user",
        password: isAdmin ? `Admin@${id}` : `User@${id}`,
      };
    })
  );

  const [showAddForm, setShowAddForm] = useState(false);
  const [search, setSearch] = useState("");
  const filteredUsers = users.filter((user) =>
    user.username.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management Portal</h1>
        <input
          type="text"
          placeholder="Search users."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button className="ml-20" onClick={() => setShowAddForm(true)}>
          Add New User
        </Button>
        {/* <Link href="@/components/settings/users/add-user">
          <Button>Create New User</Button>
        </Link> */}
      </div>

      <UserTable
        users={filteredUsers}
        onDelete={(id) => setUsers(users.filter((user) => user.id !== id))}
        onEdit={(updatedUser) =>
          setUsers(
            users.map((user) =>
              user.id === updatedUser.id ? updatedUser : user
            )
          )
        }
      />

      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg space-y-4 max-w-md w-full">
            <h2 className="text-xl font-bold">Add New User</h2>
            <AddUserForm
              onSubmit={(newUser) => {
                setUsers([...users, newUser]);
                setShowAddForm(false);
              }}
            />
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

