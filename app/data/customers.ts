// app/data/customers.ts

// Define the type for a usage record (unchanged)
export type UsageRecord = {
  customerId: number;
  startTime: string;
  endTime: string;
  sessionTime: string;
  upload: string;
  download: string;
  ipAddress: string;
  macAddress: string;
  serviceType: string;
};

// Dummy data for the usage table (unchanged)
export const usageData: UsageRecord[] = [
  {
    customerId: 1,
    startTime: "2025-05-21 12:13:53",
    endTime: "2025-05-21 12:13:53",
    sessionTime: "1 hr 2 min",
    upload: "41 MB 687 kB",
    download: "421 MB 650 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 1,
    startTime: "2025-05-18 11:20:56",
    endTime: "2025-05-21 12:05:34",
    sessionTime: "45 min",
    upload: "45 MB 748 kB",
    download: "910 MB 704 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 1,
    startTime: "2025-05-15 11:01:16",
    endTime: "2025-05-21 11:16:33",
    sessionTime: "15 min",
    upload: "4 MB 870 kB",
    download: "98 MB 131 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 1,
    startTime: "2025-05-12 12:13:53",
    endTime: "2025-05-21 12:13:53",
    sessionTime: "1 hr 2 min",
    upload: "41 MB 687 kB",
    download: "421 MB 650 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 1,
    startTime: "2025-05-10 11:20:56",
    endTime: "2025-05-21 12:05:34",
    sessionTime: "45 min",
    upload: "45 MB 748 kB",
    download: "910 MB 704 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 1,
    startTime: "2025-05-08 11:01:16",
    endTime: "2025-05-21 11:16:33",
    sessionTime: "15 min",
    upload: "4 MB 870 kB",
    download: "98 MB 131 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 2,
    startTime: "2025-05-21 12:13:53",
    endTime: "2025-05-21 12:13:53",
    sessionTime: "1 hr 2 min",
    upload: "41 MB 687 kB",
    download: "421 MB 650 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 2,
    startTime: "2025-05-21 11:20:56",
    endTime: "2025-05-21 12:05:34",
    sessionTime: "45 min",
    upload: "45 MB 748 kB",
    download: "910 MB 704 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
  {
    customerId: 2,
    startTime: "2025-05-21 11:01:16",
    endTime: "2025-05-21 11:16:33",
    sessionTime: "15 min",
    upload: "4 MB 870 kB",
    download: "98 MB 131 kB",
    ipAddress: "*************",
    macAddress: "70:C7:F2:F6:C0:9D",
    serviceType: "PPPoE",
  },
];

export type CustomerType = {
  id: number;
  name: string;
  username: string;
  mobile: string;
  // --- Enhanced Customer Details ---
  email?: string; // New: Email address
  altMobile?: string; // New: Alternative contact number
  nationalId?: string; // New: National ID (e.g., Citizenship No.)
  dateOfBirth?: string; // New: Date of Birth

  address: string; // Service Address
  billingAddress?: string; // New: Optional billing address
  latitude?: number; // New: Geo-coordinates for mapping
  longitude?: number; // New: Geo-coordinates for mapping
  landmark?: string; // New: Nearest landmark

  status: "Active" | "Inactive" | "Suspended" | "Disconnected" | "Pending" | "Expired"; // More specific status types
  registered: string; // Registration/Activation Date
  expiration: string; // Subscription Expiration Date
  package: string; // Package Name/Plan (e.g., "100Mbps Home Fiber")
  bandwidthDownload?: string; // New: e.g., "100 Mbps"
  bandwidthUpload?: string;   // New: e.g., "50 Mbps"
  contractType?: string;      // New: e.g., "1 Year"
  lastPaymentDate?: string;   // New: Last payment
  nextBillDueDate?: string;   // New: Next bill
  outstandingBalance?: string; // New: Outstanding amount

  // --- Technical Identifiers (often part of customer details) ---
  ip?: string; // Assigned IP
  mac?: string; // CPE MAC
  nasIp?: string; // NAS IP
  sessionStatus?: "Online" | "Offline" | "Idle"; // More specific session status
  onlineDuration?: string;

  // --- Router/ONT Information (as previously defined for "Router Info" tab) ---
  routerInfo?: {
    ontStatus: "Online" | "Offline" | "Faulty";
    ontSerial: string;
    ontMac: string;
    ontModel: string;
    firmwareVersion: string;
    rxPower: string;
    txPower: string;
    wanIpAddress: string;
    gatewayIp: string;
    connectionType: "PPPoE" | "DHCP" | "Static";
    vlanId: string;
    wifiSsid?: string;
    wifiMac?: string;
    onuImg?: string;
  };
};

export const customers: CustomerType[] = [
  {
    id: 1,
    status: "Active",
    username: "leomin",
    name: "Min Bahadur Malla",
    mobile: "99812345678", // Example: Updated mobile to be more realistic length
    email: "<EMAIL>", // NEW
    nationalId: "1234567890", // NEW
    dateOfBirth: "1990-01-15", // NEW
    address: "Kathmandu, Tinkune, Nepal",
    landmark: "Near XYZ School", // NEW
    expiration: "2025-06-30",
    package: "FTTH Home 100Mbps", // More specific package name
    bandwidthDownload: "100 Mbps", // NEW
    bandwidthUpload: "50 Mbps",    // NEW
    contractType: "1 Year",      // NEW
    lastPaymentDate: "2025-05-01", // NEW
    nextBillDueDate: "2025-06-01", // NEW
    outstandingBalance: "NPR 0.00", // NEW
    registered: "2024-06-01",
    ip: "*********",
    mac: "AA:BB:CC:DD:EE:FF",
    nasIp: "**********",
    sessionStatus: "Online",
    onlineDuration: "3h 20m",

    routerInfo: {
      ontStatus: "Online",
      ontSerial: "FTTX1234567890",
      ontMac: "00:11:22:33:44:55",
      ontModel: "Huawei EG8145V5",
      firmwareVersion: "V5R019C00S100",
      rxPower: "-20.5 dBm",
      txPower: "2.8 dBm",
      wanIpAddress: "**************",
      gatewayIp: "************",
      connectionType: "PPPoE",
      vlanId: "101",
      wifiSsid: "Malla_Home_Fiber",
      wifiMac: "00:11:22:33:44:56",
      onuImg: "/images/onu.png",
    }
  },
  {
    id: 2,
    status: "Active",
    username: "swiftnet123",
    name: "Swiftnet Pvt. Ltd.",
    mobile: "9800000000",
    email: "<EMAIL>", // NEW
    address: "Lalitpur, Commercial Complex 4B",
    expiration: "2025-03-15",
    package: "FTTH Business 200Mbps",
    bandwidthDownload: "200 Mbps", // NEW
    bandwidthUpload: "100 Mbps",   // NEW
    contractType: "6 Months",    // NEW
    registered: "2023-09-12",
    ip: "*********",
    mac: "BB:CC:DD:EE:FF:11",
    nasIp: "**********",
    sessionStatus: "Online",
    onlineDuration: "3h 20m",

    routerInfo: {
      ontStatus: "Online",
      ontSerial: "FTTX1234567890",
      ontMac: "00:11:22:33:44:55",
      ontModel: "Huawei EG8145V5",
      firmwareVersion: "V5R019C00S100",
      rxPower: "-20.5 dBm",
      txPower: "2.8 dBm",
      wanIpAddress: "**************",
      gatewayIp: "************",
      connectionType: "PPPoE",
      vlanId: "101",
      wifiSsid: "Swift_Home_Fiber",
      wifiMac: "00:11:22:33:44:56",
    }
  },
  // Add more customer data here if needed, with or without routerInfo
  {
    id: 3,
    status: "Active",
    username: "cnc",
    name: "cnc Pvt. Ltd.",
    mobile: "9800000000",
    address: "Lalitpur",
    expiration: "2025-03-15",
    package: "200Mbps",
    registered: "2023-09-12",
    sessionStatus: "Online",
  },
  {
    id: 4,
    status: "Expired",
    username: "niig",
    name: "cnc Pvt. Ltd.",
    mobile: "9800000000",
    address: "Lalitpur",
    expiration: "2025-03-15",
    package: "200Mbps",
    registered: "2023-09-12",
  },
  {
    id: 5,
    status: "Expired",
    username: "workalaya",
    name: "workalaya Pvt. Ltd.",
    mobile: "9800000000",
    address: "Lalitpur",
    expiration: "2025-03-15",
    package: "200Mbps",
    registered: "2023-09-12",
  },
  {
    id: 6,
    status: "Expired",
    username: "sky",
    name: "sky Pvt. Ltd.",
    mobile: "9800000000",
    address: "Lalitpur",
    expiration: "2025-03-15",
    package: "200Mbps",
    registered: "2023-09-12",
  },
  {
    id: 7,
    status: "Expired",
    username: "redeyes",
    name: "RedEyes Pvt. Ltd.",
    mobile: "9800000000",
    address: "Lalitpur",
    expiration: "2025-03-15",
    package: "200Mbps",
    registered: "2023-09-12",
  },

  // New Random Customers
  {
    id: 8,
    status: "Active",
    username: "ramthapa",
    name: "Ram Thapa",
    mobile: "9811122334",
    address: "Pokhara",
    expiration: "2025-12-31",
    package: "50Mbps",
    registered: "2024-01-10",
    ip: "*********",
    mac: "CC:DD:EE:FF:00:11",
    nasIp: "**********",
    sessionStatus: "Online",
    onlineDuration: "1h 45m",
  },
  {
    id: 9,
    status: "Inactive",
    username: "neptelecom",
    name: "Nep Telecom Pvt. Ltd.",
    mobile: "9801234567",
    address: "Biratnagar",
    expiration: "2024-08-10",
    package: "150Mbps",
    registered: "2023-04-05",
  },
  {
    id: 10,
    status: "Expired",
    username: "laxmi",
    name: "Laxmi Shrestha",
    mobile: "9809876543",
    address: "Butwal",
    expiration: "2024-04-01",
    package: "25Mbps",
    registered: "2022-07-12",
  },
  {
    id: 11,
    status: "Active",
    username: "kpmc",
    name: "KPMC Pvt. Ltd.",
    mobile: "9803344556",
    address: "Kathmandu",
    expiration: "2026-01-15",
    package: "500Mbps",
    registered: "2024-02-25",
    ip: "*********1",
    mac: "11:22:33:44:55:66",
    nasIp: "**********1",
    sessionStatus: "Online",
    onlineDuration: "5h 12m",
  },
  {
    id: 12,
    status: "Inactive",
    username: "priya",
    name: "Priya Gurung",
    mobile: "9807654321",
    address: "Dharan",
    expiration: "2025-05-20",
    package: "75Mbps",
    registered: "2023-08-30",
  },
  {
    id: 13,
    status: "Active",
    username: "shivacorp",
    name: "Shiva Corporation",
    mobile: "9810011223",
    address: "Kathmandu",
    expiration: "2025-11-30",
    package: "100Mbps",
    registered: "2023-10-05",
    ip: "**********",
    mac: "22:33:44:55:66:77",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "2h 35m",
  },
  {
    id: 14,
    status: "Expired",
    username: "ramco",
    name: "Ram Co.",
    mobile: "9809988776",
    address: "Janakpur",
    expiration: "2023-12-15",
    package: "30Mbps",
    registered: "2022-12-10",
  },
  {
    id: 15,
    status: "Active",
    username: "tulsinet",
    name: "Tulsi Net",
    mobile: "9812341223",
    address: "Kathmandu",
    expiration: "2025-09-30",
    package: "250Mbps",
    registered: "2024-03-22",
    ip: "**********",
    mac: "33:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "4h 0m",
  },
  {
    id: 16,
    status: "Inactive",
    username: "kamal",
    name: "Kamal Thapa",
    mobile: "9804455667",
    address: "Bharatpur",
    expiration: "2024-10-01",
    package: "20Mbps",
    registered: "2023-11-01",
  },
  {
    id: 17,
    status: "Active",
    username: "bikash",
    name: "Bikash Lama",
    mobile: "9812233445",
    address: "Kathmandu",
    expiration: "2026-05-15",
    package: "150Mbps",
    registered: "2024-05-01",
    ip: "**********",
    mac: "44:55:66:77:88:99",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "6h 15m",
  },
  {
    id: 18,
    status: "Expired",
    username: "sudip",
    name: "Sudip Rai",
    mobile: "9801234432",
    address: "Hetauda",
    expiration: "2024-01-05",
    package: "40Mbps",
    registered: "2022-01-10",
  },
  {
    id: 19,
    status: "Active",
    username: "mahesh",
    name: "Mahesh KC",
    mobile: "9812456712",
    address: "Kathmandu",
    expiration: "2026-12-31",
    package: "300Mbps",
    registered: "2024-01-20",
    ip: "**********",
    mac: "55:66:77:88:99:AA",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "7h 40m",
  },
  {
    id: 20,
    status: "Inactive",
    username: "xyzcorp",
    name: "XYZ Corporation",
    mobile: "9809998887",
    address: "Lalitpur",
    expiration: "2025-04-15",
    package: "120Mbps",
    registered: "2023-07-07",
  },
  {
    id: 21,
    status: "Active",
    username: "sumanthapa",
    name: "Suman Thapa",
    mobile: "9811122335",
    address: "Kathmandu",
    expiration: "2025-10-30",
    package: "80Mbps",
    registered: "2023-12-01",
    ip: "**********",
    mac: "66:77:88:99:AA:BB",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "2h 50m",
  },
  {
    id: 22,
    status: "Expired",
    username: "abcnet",
    name: "ABC Net",
    mobile: "9807766554",
    address: "Birgunj",
    expiration: "2023-09-30",
    package: "60Mbps",
    registered: "2022-09-15",
  },
  {
    id: 23,
    status: "Inactive",
    username: "dipesh",
    name: "Dipesh Gautam",
    mobile: "9806655443",
    address: "Kathmandu",
    expiration: "2024-06-20",
    package: "90Mbps",
    registered: "2023-03-15",
  },
  {
    id: 24,
    status: "Active",
    username: "pratiksha",
    name: "Pratiksha Shrestha",
    mobile: "9813344556",
    address: "Kathmandu",
    expiration: "2026-03-10",
    package: "180Mbps",
    registered: "2024-04-11",
    ip: "**********",
    mac: "77:88:99:AA:BB:CC",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "3h 05m",
  },
  {
    id: 25,
    status: "Active",
    username: "sunil",
    name: "Sunil KC",
    mobile: "9805566778",
    address: "Kathmandu",
    expiration: "2025-07-22",
    package: "75Mbps",
    registered: "2023-10-18",
    ip: "**********",
    mac: "88:99:AA:BB:CC:DD",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "4h 30m",
  },
  {
    id: 26,
    status: "Inactive",
    username: "amrit",
    name: "Amrit Basnet",
    mobile: "9804433221",
    address: "Dhangadhi",
    expiration: "2024-11-01",
    package: "100Mbps",
    registered: "2023-06-01",

    routerInfo: {
      ontStatus: "Online",
      ontSerial: "FTTX1234567890",
      ontMac: "00:11:22:33:44:55",
      ontModel: "Huawei EG8145V5",
      firmwareVersion: "V5R019C00S100",
      rxPower: "-20.5 dBm",
      txPower: "2.8 dBm",
      wanIpAddress: "**************",
      gatewayIp: "************",
      connectionType: "PPPoE",
      vlanId: "101",
      wifiSsid: "Malla_Home_Fiber",
      wifiMac: "00:11:22:33:44:56",
      onuImg: "/images/onu.png",
    }
  },
  {
    id: 27,
    status: "Expired",
    username: "hari",
    name: "Hari Sharma",
    mobile: "9803322114",
    address: "Kathmandu",
    expiration: "2023-08-20",
    package: "40Mbps",
    registered: "2022-05-22",
  },
  {
    id: 28,
    status: "Active",
    username: "manoj",
    name: "Manoj Karki",
    mobile: "9817788990",
    address: "Pokhara",
    expiration: "2026-07-30",
    package: "200Mbps",
    registered: "2024-02-12",
    ip: "**********",
    mac: "99:AA:BB:CC:DD:EE",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "5h 45m",
  },
  {
    id: 29,
    status: "Inactive",
    username: "tara",
    name: "Tara Devi",
    mobile: "9808899776",
    address: "Kathmandu",
    expiration: "2025-01-15",
    package: "60Mbps",
    registered: "2023-01-10",
  },
  {
    id: 30,
    status: "Active",
    username: "samir",
    name: "Samir Thapa",
    mobile: "9813344221",
    address: "Kathmandu",
    expiration: "2026-10-31",
    package: "350Mbps",
    registered: "2024-03-20",
    ip: "**********",
    mac: "AA:BB:CC:DD:EE:11",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "6h 10m",
  },
  {
    id: 31,
    status: "Expired",
    username: "bishal",
    name: "Bishal Shrestha",
    mobile: "9801122334",
    address: "Kathmandu",
    expiration: "2024-05-05",
    package: "80Mbps",
    registered: "2022-11-12",
  },
  {
    id: 32,
    status: "Active",
    username: "jyoti",
    name: "Jyoti Rana",
    mobile: "9812345566",
    address: "Lalitpur",
    expiration: "2025-08-20",
    package: "150Mbps",
    registered: "2023-09-17",
    ip: "**********",
    mac: "BB:CC:DD:EE:FF:22",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "4h 55m",
  },
  {
    id: 33,
    status: "Inactive",
    username: "neha",
    name: "Neha Sharma",
    mobile: "9806677889",
    address: "Butwal",
    expiration: "2025-02-10",
    package: "70Mbps",
    registered: "2023-05-21",
  },
  {
    id: 34,
    status: "Active",
    username: "rohan",
    name: "Rohan Thapa",
    mobile: "9812233446",
    address: "Kathmandu",
    expiration: "2026-04-15",
    package: "250Mbps",
    registered: "2024-01-15",
    ip: "**********",
    mac: "CC:DD:EE:FF:11:33",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "3h 25m",
  },
  {
    id: 35,
    status: "Expired",
    username: "bina",
    name: "Bina Rai",
    mobile: "9802233445",
    address: "Hetauda",
    expiration: "2023-07-01",
    package: "55Mbps",
    registered: "2021-12-12",
  },
  {
  id: 36,
  status: "Active",
  username: "netlink36",
  name: "Netlink 36",
  mobile: "9800000036",
  address: "Pokhara",
  expiration: "2025-12-01",
  package: "100Mbps",
  registered: "2024-01-15",
  ip: "**********",
  mac: "00:11:22:33:44:36",
  nasIp: "***********",
  sessionStatus: "Online",
  onlineDuration: "2h 18m",
},
{
  id: 37,
  status: "Expired",
  username: "skyfiber37",
  name: "Sky Fiber",
  mobile: "9800000037",
  address: "Butwal",
  expiration: "2023-11-01",
  package: "20Mbps",
  registered: "2022-10-12",
},
{
  id: 38,
  status: "Active",
  username: "connect38",
  name: "Connect ISP",
  mobile: "9800000038",
  address: "Dhangadhi",
  expiration: "2026-02-10",
  package: "50Mbps",
  registered: "2024-06-18",
  ip: "**********",
  mac: "00:11:22:33:44:38",
  nasIp: "***********",
  sessionStatus: "Offline",
  onlineDuration: "0h 0m",
},
{
  id: 39,
  status: "Inactive",
  username: "vipnet39",
  name: "Vip Net",
  mobile: "9800000039",
  address: "Birgunj",
  expiration: "2024-09-05",
  package: "25Mbps",
  registered: "2023-04-05",
},
{
  id: 40,
  status: "Active",
  username: "digitalnet40",
  name: "Digital Net",
  mobile: "9800000040",
  address: "Hetauda",
  expiration: "2025-07-22",
  package: "150Mbps",
  registered: "2024-05-01",
  ip: "**********",
  mac: "00:11:22:33:44:40",
  nasIp: "***********",
  sessionStatus: "Online",
  onlineDuration: "7h 45m",
},
{
  id: 41,
  status: "Expired",
  username: "user41",
  name: "User 41",
  mobile: "9800000041",
  address: "Janakpur",
  expiration: "2023-02-22",
  package: "100Mbps",
  registered: "2023-05-26",
},
{
  id: 42,
  status: "Active",
  username: "user42",
  name: "User 42",
  mobile: "9800000042",
  address: "Bharatpur",
  expiration: "2026-09-08",
  package: "50Mbps",
  registered: "2023-10-03",
  ip: "**********",
  mac: "00:11:22:33:44:2a",
  nasIp: "***********",
  sessionStatus: "Online",
  onlineDuration: "8h 6m",
},
{
  id: 43,
  status: "Active",
  username: "user43",
  name: "User 43",
  mobile: "9800000043",
  address: "Janakpur",
  expiration: "2024-01-25",
  package: "20Mbps",
  registered: "2022-06-06",
  ip: "**********",
  mac: "00:11:22:33:44:2b",
  nasIp: "***********",
  sessionStatus: "Offline",
  onlineDuration: "0h 0m",
},
{
  id: 44,
  status: "Inactive",
  username: "user44",
  name: "User 44",
  mobile: "9800000044",
  address: "Bharatpur",
  expiration: "2023-05-31",
  package: "50Mbps",
  registered: "2022-04-01",
},
{
  id: 45,
  status: "Active",
  username: "user45",
  name: "User 45",
  mobile: "9800000045",
  address: "Itahari",
  expiration: "2026-11-23",
  package: "200Mbps",
  registered: "2024-09-18",
  ip: "**********",
  mac: "00:11:22:33:44:2d",
  nasIp: "***********",
  sessionStatus: "Offline",
  onlineDuration: "0h 0m",
},

  {
    id: 46,
    status: "Active",
    username: "nepalwire",
    name: "Nepal Wireless",
    mobile: "9801122334",
    address: "Pokhara",
    expiration: "2025-11-20",
    package: "150Mbps",
    registered: "2024-02-15",
    ip: "**********",
    mac: "36:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "2h 45m"
  },
  {
    id: 47,
    status: "Expired",
    username: "himalconnect",
    name: "Himal Connect",
    mobile: "9812233445",
    address: "Dharan",
    expiration: "2023-10-31",
    package: "50Mbps",
    registered: "2022-08-12"
  },
  {
    id: 48,
    status: "Active",
    username: "techvalley",
    name: "Tech Valley Nepal",
    mobile: "9823344556",
    address: "Biratnagar",
    expiration: "2026-01-15",
    package: "500Mbps",
    registered: "2024-05-10",
    ip: "**********",
    mac: "42:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "6h 30m"
  },
  {
    id: 49,
    status: "Inactive",
    username: "broadnet",
    name: "Broad Net Services",
    mobile: "9834455667",
    address: "Butwal",
    expiration: "2025-04-30",
    package: "100Mbps",
    registered: "2023-11-20",
    ip: "**********",
    mac: "45:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Offline",
    onlineDuration: "-"
  },
  {
    id: 50,
    status: "Expired",
    username: "linknepal",
    name: "Link Nepal",
    mobile: "9845566778",
    address: "Hetauda",
    expiration: "2024-02-28",
    package: "40Mbps",
    registered: "2022-09-05"
  },
  {
    id: 51,
    status: "Active",
    username: "cyberworld",
    name: "Cyber World Nepal",
    mobile: "9856677889",
    address: "Bharatpur",
    expiration: "2025-12-10",
    package: "300Mbps",
    registered: "2024-04-18",
    ip: "**********",
    mac: "57:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "5h 15m"
  },
  {
    id: 52,
    status: "Inactive",
    username: "netplus",
    name: "Net Plus Communications",
    mobile: "9867788990",
    address: "Dhangadhi",
    expiration: "2025-06-15",
    package: "80Mbps",
    registered: "2023-12-01",
    ip: "**********",
    mac: "63:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Offline",
    onlineDuration: "-"
  },
  {
    id: 53,
    status: "Expired",
    username: "fiberworld",
    name: "Fiber World",
    mobile: "9878899001",
    address: "Itahari",
    expiration: "2023-12-31",
    package: "60Mbps",
    registered: "2022-10-15"
  },
  {
    id: 54,
    status: "Active",
    username: "speedlink",
    name: "Speed Link Nepal",
    mobile: "9889900112",
    address: "Janakpur",
    expiration: "2026-02-28",
    package: "200Mbps",
    registered: "2024-06-05",
    ip: "**********",
    mac: "72:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "3h 50m"
  },
  {
    id: 55,
    status: "Inactive",
    username: "megaconnect",
    name: "Mega Connect",
    mobile: "9890011223",
    address: "Nepalgunj",
    expiration: "2025-05-20",
    package: "120Mbps",
    registered: "2023-10-10",
    ip: "**********",
    mac: "79:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Offline",
    onlineDuration: "-"
  },
  {
    id: 56,
    status: "Expired",
    username: "ultralink",
    name: "Ultra Link",
    mobile: "9801122335",
    address: "Birgunj",
    expiration: "2024-03-15",
    package: "35Mbps",
    registered: "2022-07-22"
  },
  {
    id: 57,
    status: "Active",
    username: "netexpress",
    name: "Net Express",
    mobile: "9812233446",
    address: "Dhankuta",
    expiration: "2025-10-10",
    package: "180Mbps",
    registered: "2024-01-15",
    ip: "**********",
    mac: "90:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Online",
    onlineDuration: "4h 45m"
  },
  {
    id: 58,
    status: "Inactive",
    username: "webworld",
    name: "Web World Nepal",
    mobile: "9823344557",
    address: "Tansen",
    expiration: "2025-07-31",
    package: "90Mbps",
    registered: "2023-09-05",
    ip: "**********",
    mac: "95:44:55:66:77:88",
    nasIp: "***********",
    sessionStatus: "Offline",
    onlineDuration: "-"
  },
  {
    id: 59,
    status: "Active",
    username: "skybroadband",
    name: "Sky Broadband",
    mobile: "9834455668",
    address: "Mahendranagar",
    expiration: "2026-03-15",
    package: "400Mbps",
    registered: "2024-06-20",
    ip: "***********",
    mac: "00:44:55:66:77:88",
    nasIp: "************",
    sessionStatus: "Online",
    onlineDuration: "7h 20m"
  },
  {
    "id": 60,
    "status": "Active",
    "username": "speednet",
    "name": "Speed Net",
    "mobile": "9801234560",
    "address": "Pokhara",
    "expiration": "2025-11-01",
    "package": "50Mbps",
    "registered": "2023-12-10",
    "ip": "**********",
    "mac": "00:1A:2B:3C:4D:60",
    "nasIp": "***********",
    "sessionStatus": "Online",
    "onlineDuration": "2h 10m"
    },
    {
      "id": 61,
      "status": "Expired",
      "username": "himalnet",
      "name": "Himal Net",
      "mobile": "9811122334",
      "address": "Butwal",
      "expiration": "2023-12-20",
      "package": "25Mbps",
      "registered": "2022-08-01"
    },
    {
      "id": 62,
      "status": "Active",
      "username": "smartfiber",
      "name": "Smart Fiber",
      "mobile": "9804455667",
      "address": "Nepalgunj",
      "expiration": "2026-01-12",
      "package": "70Mbps",
      "registered": "2024-04-15",
      "ip": "**********",
      "mac": "22:33:44:55:66:62",
      "nasIp": "***********",
      "sessionStatus": "Offline"
    },
    {
      "id": 63,
      "status": "Active",
      "username": "everestnet",
      "name": "Everest Net",
      "mobile": "9805566778",
      "address": "Dharan",
      "expiration": "2025-09-01",
      "package": "100Mbps",
      "registered": "2024-06-18"
    },
    {
      "id": 64,
      "status": "Expired",
      "username": "lightlink",
      "name": "Light Link",
      "mobile": "9812345670",
      "address": "Chitwan",
      "expiration": "2024-02-28",
      "package": "40Mbps",
      "registered": "2023-01-25"
    },
    {
      "id": 65,
      "status": "Active",
      "username": "nepalnet",
      "name": "Nepal Net",
      "mobile": "9807788990",
      "address": "Hetauda",
      "expiration": "2025-12-10",
      "package": "60Mbps",
      "registered": "2024-03-05",
      "ip": "**********",
      "mac": "11:22:33:44:55:65",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "6h 5m"
    },
    {
      "id": 66,
      "status": "Expired",
      "username": "skyconnect",
      "name": "Sky Connect",
      "mobile": "9803344556",
      "address": "Lalitpur",
      "expiration": "2023-11-15",
      "package": "20Mbps",
      "registered": "2021-07-19"
    },
    {
      "id": 67,
      "status": "Active",
      "username": "digitalwave",
      "name": "Digital Wave",
      "mobile": "9809988776",
      "address": "Itahari",
      "expiration": "2025-07-25",
      "package": "45Mbps",
      "registered": "2023-08-11",
      "ip": "**********",
      "mac": "AA:BB:CC:DD:EE:67",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "1h 20m"
    },
    {
      "id": 68,
      "status": "Expired",
      "username": "rapidnet",
      "name": "Rapid Net",
      "mobile": "9819988776",
      "address": "Janakpur",
      "expiration": "2024-04-05",
      "package": "30Mbps",
      "registered": "2022-03-22"
    },
    {
      "id": 69,
      "status": "Active",
      "username": "fiberzone",
      "name": "Fiber Zone",
      "mobile": "9811223344",
      "address": "Damak",
      "expiration": "2025-08-15",
      "package": "75Mbps",
      "registered": "2024-05-20",
      "ip": "**********",
      "mac": "DE:AD:BE:EF:00:69",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "3h 30m"
    },
    {
      "id": 70,
      "status": "Expired",
      "username": "netbuzz",
      "name": "Net Buzz",
      "mobile": "9803344550",
      "address": "Bhaktapur",
      "expiration": "2023-10-01",
      "package": "15Mbps",
      "registered": "2021-11-30"
    },
    {
      "id": 71,
      "status": "Active",
      "username": "connecthub",
      "name": "Connect Hub",
      "mobile": "9812233440",
      "address": "Banepa",
      "expiration": "2025-12-01",
      "package": "85Mbps",
      "registered": "2024-02-14",
      "ip": "**********",
      "mac": "00:11:22:33:44:71",
      "nasIp": "***********",
      "sessionStatus": "Offline"
    },
    {
      "id": 72,
      "status": "Expired",
      "username": "techwave",
      "name": "Tech Wave",
      "mobile": "9805566771",
      "address": "Biratnagar",
      "expiration": "2024-01-01",
      "package": "50Mbps",
      "registered": "2022-02-28"
    },
    {
      "id": 73,
      "status": "Active",
      "username": "fastfiber",
      "name": "Fast Fiber",
      "mobile": "9801122998",
      "address": "Lamjung",
      "expiration": "2025-06-30",
      "package": "120Mbps",
      "registered": "2024-03-21",
      "ip": "**********",
      "mac": "FE:ED:FA:CE:00:73",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "5h 55m"
    },
    {
      "id": 74,
      "status": "Expired",
      "username": "goldlink",
      "name": "Gold Link",
      "mobile": "9806677880",
      "address": "Gorkha",
      "expiration": "2023-09-30",
      "package": "20Mbps",
      "registered": "2021-12-15"
    },
    {
      "id": 75,
      "status": "Active",
      "username": "supernet",
      "name": "Super Net",
      "mobile": "9804433221",
      "address": "Bharatpur",
      "expiration": "2025-03-10",
      "package": "90Mbps",
      "registered": "2024-01-01",
      "ip": "**********",
      "mac": "CC:DD:EE:FF:AA:75",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "7h 15m"
    },
    {
      "id": 76,
      "status": "Expired",
      "username": "nepaltele",
      "name": "Nepal Tele",
      "mobile": "9810022334",
      "address": "Dang",
      "expiration": "2024-05-10",
      "package": "35Mbps",
      "registered": "2022-06-12"
    },
    {
      "id": 77,
      "status": "Active",
      "username": "skyfiber",
      "name": "Sky Fiber",
      "mobile": "9801234007",
      "address": "Kirtipur",
      "expiration": "2025-11-11",
      "package": "100Mbps",
      "registered": "2024-05-01",
      "ip": "**********",
      "mac": "AB:CD:EF:01:23:77",
      "nasIp": "***********",
      "sessionStatus": "Offline"
    },
    {
      "id": 78,
      "status": "Expired",
      "username": "techconnect",
      "name": "Tech Connect",
      "mobile": "9812233000",
      "address": "Tulsipur",
      "expiration": "2023-08-22",
      "package": "25Mbps",
      "registered": "2022-03-03"
    },
    {
      "id": 79,
      "status": "Active",
      "username": "fibervault",
      "name": "Fiber Vault",
      "mobile": "9807766554",
      "address": "Gaur",
      "expiration": "2026-02-05",
      "package": "200Mbps",
      "registered": "2024-07-01",
      "ip": "**********",
      "mac": "98:76:54:32:10:79",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "4h 10m"
    },
    {
      "id": 80,
      "status": "Expired",
      "username": "waveconnect",
      "name": "Wave Connect",
      "mobile": "9803344567",
      "address": "Siraha",
      "expiration": "2024-02-14",
      "package": "40Mbps",
      "registered": "2022-04-01"
    },
        {
      "id": 81,
      "status": "Active",
      "username": "lightnet",
      "name": "Light Net",
      "mobile": "9801011122",
      "address": "Lalitpur",
      "expiration": "2025-12-30",
      "package": "50Mbps",
      "registered": "2023-05-10",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:01",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "1h 12m"
    },
    {
      "id": 82,
      "status": "Expired",
      "username": "speedline",
      "name": "Speed Line",
      "mobile": "9801022233",
      "address": "Butwal",
      "expiration": "2024-04-15",
      "package": "30Mbps",
      "registered": "2022-08-21"
    },
    {
      "id": 83,
      "status": "Inactive",
      "username": "skyconnect",
      "name": "Sky Connect",
      "mobile": "9811002233",
      "address": "Hetauda",
      "expiration": "2025-01-10",
      "package": "20Mbps",
      "registered": "2023-01-02"
    },
    {
      "id": 84,
      "status": "Active",
      "username": "techfiber",
      "name": "Tech Fiber",
      "mobile": "9809988776",
      "address": "Pokhara",
      "expiration": "2025-11-11",
      "package": "100Mbps",
      "registered": "2024-02-20",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:04",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "3h 27m"
    },
    {
      "id": 85,
      "status": "Expired",
      "username": "everestlink",
      "name": "Everest Link",
      "mobile": "9812003344",
      "address": "Nepalgunj",
      "expiration": "2024-12-01",
      "package": "40Mbps",
      "registered": "2022-09-12"
    },
    {
      "id": 86,
      "status": "Active",
      "username": "maxinet",
      "name": "Maxi Net",
      "mobile": "9803344556",
      "address": "Bharatpur",
      "expiration": "2026-01-01",
      "package": "75Mbps",
      "registered": "2023-03-05",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:06",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "5h 10m"
    },
    {
      "id": 87,
      "status": "Inactive",
      "username": "fiberworld",
      "name": "Fiber World",
      "mobile": "9804455667",
      "address": "Janakpur",
      "expiration": "2025-03-20",
      "package": "25Mbps",
      "registered": "2022-11-25"
    },
    {
      "id": 88,
      "status": "Active",
      "username": "infranet",
      "name": "Infra Net",
      "mobile": "9811223344",
      "address": "Dhangadhi",
      "expiration": "2025-08-08",
      "package": "150Mbps",
      "registered": "2024-04-01",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:08",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "6h 50m"
    },
    {
      "id": 89,
      "status": "Expired",
      "username": "orbitnet",
      "name": "Orbit Net",
      "mobile": "9805566778",
      "address": "Bhairahawa",
      "expiration": "2023-10-30",
      "package": "15Mbps",
      "registered": "2021-12-12"
    },
    {
      "id": 90,
      "status": "Active",
      "username": "flashconnect",
      "name": "Flash Connect",
      "mobile": "9813344556",
      "address": "Itahari",
      "expiration": "2025-07-14",
      "package": "60Mbps",
      "registered": "2023-06-30",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:0A",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "2h 35m"
    },
    {
      "id": 91,
      "status": "Inactive",
      "username": "trinet",
      "name": "Tri Net",
      "mobile": "9806677889",
      "address": "Gorkha",
      "expiration": "2024-06-18",
      "package": "20Mbps",
      "registered": "2022-10-22"
    },
    {
      "id": 92,
      "status": "Active",
      "username": "quantumnet",
      "name": "Quantum Net",
      "mobile": "9814455667",
      "address": "Tikapur",
      "expiration": "2025-11-25",
      "package": "90Mbps",
      "registered": "2024-01-18",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:0C",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "4h 20m"
    },
    {
      "id": 93,
      "status": "Expired",
      "username": "netlinkplus",
      "name": "Net Link Plus",
      "mobile": "9807788990",
      "address": "Banepa",
      "expiration": "2024-02-18",
      "package": "50Mbps",
      "registered": "2022-06-01"
    },
    {
      "id": 94,
      "status": "Active",
      "username": "fibermax",
      "name": "Fiber Max",
      "mobile": "9808899001",
      "address": "Dharan",
      "expiration": "2025-04-01",
      "package": "100Mbps",
      "registered": "2023-07-07",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:0E",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "7h 15m"
    },
    {
      "id": 95,
      "status": "Inactive",
      "username": "pokharanet",
      "name": "Pokhara Net",
      "mobile": "9815566778",
      "address": "Pokhara",
      "expiration": "2023-12-01",
      "package": "25Mbps",
      "registered": "2021-09-19"
    },
    {
      "id": 96,
      "status": "Active",
      "username": "xstreamnet",
      "name": "Xstream Net",
      "mobile": "9809911223",
      "address": "Palpa",
      "expiration": "2026-02-15",
      "package": "70Mbps",
      "registered": "2024-05-12",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:10",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "2h 18m"
    },
    {
      "id": 97,
      "status": "Expired",
      "username": "cloudnet",
      "name": "Cloud Net",
      "mobile": "9816677889",
      "address": "Chitwan",
      "expiration": "2023-08-09",
      "package": "35Mbps",
      "registered": "2021-08-20"
    },
    {
      "id": 98,
      "status": "Active",
      "username": "mtel",
      "name": "MTel",
      "mobile": "9801234432",
      "address": "Dang",
      "expiration": "2025-12-20",
      "package": "200Mbps",
      "registered": "2024-01-30",
      "ip": "**********",
      "mac": "A0:B1:C2:D3:E4:12",
      "nasIp": "***********",
      "sessionStatus": "Online",
      "onlineDuration": "8h 01m"
    },
    {
      "id": 99,
      "status": "Inactive",
      "username": "nexgenfiber",
      "name": "NexGen Fiber",
      "mobile": "9801112233",
      "address": "Kavre",
      "expiration": "2024-01-15",
      "package": "60Mbps",
      "registered": "2023-04-22"
    },
    {
      "id": 100,
      "status": "Active",
      "username": "uninet",
      "name": "Uni Net",
      "mobile": "9819988776",
      "address": "Baglung",
      "expiration": "2025-10-10",
      "package": "120Mbps",
      "registered": "2024-02-14",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:14",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "3h 59m"
    },
        {
      "id": 101,
      "status": "Active",
      "username": "techwave",
      "name": "Tech Wave",
      "mobile": "9811122334",
      "address": "Pokhara",
      "expiration": "2025-06-30",
      "package": "100Mbps",
      "registered": "2024-01-05",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:15",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "1h 45m"
    },
    {
      "id": 102,
      "status": "Inactive",
      "username": "netblaze",
      "name": "Net Blaze",
      "mobile": "9823344556",
      "address": "Butwal",
      "expiration": "2024-03-15",
      "package": "40Mbps",
      "registered": "2023-11-20"
    },
    {
      "id": 103,
      "status": "Active",
      "username": "digitalhub",
      "name": "Digital Hub",
      "mobile": "9834455667",
      "address": "Biratnagar",
      "expiration": "2025-09-20",
      "package": "80Mbps",
      "registered": "2024-02-01",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:16",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "5h 22m"
    },
    {
      "id": 104,
      "status": "Active",
      "username": "cyberlink",
      "name": "Cyber Link",
      "mobile": "9845566778",
      "address": "Dharan",
      "expiration": "2025-07-15",
      "package": "120Mbps",
      "registered": "2023-12-10",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:17",
      "nasIp": "************",
      "sessionStatus": "Offline"
    },
    {
      "id": 105,
      "status": "Inactive",
      "username": "fiberone",
      "name": "Fiber One",
      "mobile": "9856677889",
      "address": "Bharatpur",
      "expiration": "2024-02-28",
      "package": "60Mbps",
      "registered": "2023-08-15"
    },
    {
      "id": 106,
      "status": "Active",
      "username": "netvelocity",
      "name": "Net Velocity",
      "mobile": "9867788990",
      "address": "Hetauda",
      "expiration": "2025-08-05",
      "package": "150Mbps",
      "registered": "2024-01-20",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:18",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "2h 10m"
    },
    {
      "id": 107,
      "status": "Active",
      "username": "broadbandplus",
      "name": "Broadband Plus",
      "mobile": "9878899001",
      "address": "Janakpur",
      "expiration": "2025-11-30",
      "package": "200Mbps",
      "registered": "2023-10-15",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:19",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "7h 45m"
    },
    {
      "id": 108,
      "status": "Inactive",
      "username": "megaconnect",
      "name": "Mega Connect",
      "mobile": "9889900112",
      "address": "Itahari",
      "expiration": "2024-01-10",
      "package": "50Mbps",
      "registered": "2023-07-22"
    },
    {
      "id": 109,
      "status": "Active",
      "username": "ultranet",
      "name": "Ultra Net",
      "mobile": "9890011223",
      "address": "Nepalgunj",
      "expiration": "2025-12-15",
      "package": "100Mbps",
      "registered": "2024-03-01",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:20",
      "nasIp": "************",
      "sessionStatus": "Offline"
    },
    {
      "id": 110,
      "status": "Active",
      "username": "speedlink",
      "name": "Speed Link",
      "mobile": "9801122334",
      "address": "Dhangadhi",
      "expiration": "2025-09-05",
      "package": "80Mbps",
      "registered": "2023-11-15",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:21",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "4h 30m"
    },
    {
      "id": 111,
      "status": "Inactive",
      "username": "netexpress",
      "name": "Net Express",
      "mobile": "9812233445",
      "address": "Birgunj",
      "expiration": "2024-04-20",
      "package": "40Mbps",
      "registered": "2023-09-10"
    },
    {
      "id": 112,
      "status": "Active",
      "username": "fibermax",
      "name": "Fiber Max",
      "mobile": "9823344556",
      "address": "Bhaktapur",
      "expiration": "2025-10-25",
      "package": "120Mbps",
      "registered": "2024-02-05",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:22",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "6h 15m"
    },
    {
      "id": 113,
      "status": "Active",
      "username": "hypernet",
      "name": "Hyper Net",
      "mobile": "9834455667",
      "address": "Lalitpur",
      "expiration": "2025-08-15",
      "package": "160Mbps",
      "registered": "2023-12-20",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:23",
      "nasIp": "************",
      "sessionStatus": "Offline"
    },
    {
      "id": 114,
      "status": "Inactive",
      "username": "quicklink",
      "name": "Quick Link",
      "mobile": "9845566778",
      "address": "Kirtipur",
      "expiration": "2024-05-10",
      "package": "30Mbps",
      "registered": "2023-10-05"
    },
    {
      "id": 115,
      "status": "Active",
      "username": "supremeconnect",
      "name": "Supreme Connect",
      "mobile": "9856677889",
      "address": "Tansen",
      "expiration": "2025-11-20",
      "package": "200Mbps",
      "registered": "2024-01-15",
      "ip": "***********",
      "mac": "A0:B1:C2:D3:E4:24",
      "nasIp": "************",
      "sessionStatus": "Online",
      "onlineDuration": "8h 05m"
    }
];
