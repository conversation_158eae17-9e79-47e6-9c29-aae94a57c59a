// app/root-layout.tsx
import "./globals.css";
import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";

const inter = Inter({ subsets: ["latin"] });

export const commonMetadata: Metadata = {
  title: {
    default: "Workalaya Dashboard",
    template: "%s | Workalaya",
  },
  description: "Client management dashboard",
  icons: {
    icon: "/images/workalaya-icon.png",
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    // Apply suppressHydrationWarning to the <html> tag for next-themes
    <html lang="en" suppressHydrationWarning>
      {/* Apply suppressHydrationWarning to the <body> tag as well,
          to handle browser extensions or other client-side DOM manipulations. */}
      <body className={inter.className} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}